{{ if not .Values.postgresql.auth.existingSecret }}
apiVersion: v1
kind: Secret
metadata:
  name: librechat-vectordb
type: Opaque
data:
  # generate 32 chars long random string, base64 encode it and then double-quote the result string.
  postgres-password: {{ randAlphaNum 32 | b64enc | quote }}
  password: {{ randAlphaNum 32 | b64enc | quote }}
  replication-password: {{ randAlphaNum 32 | b64enc | quote }}
{{ end }}