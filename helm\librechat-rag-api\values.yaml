# Required as vectorDB for RAG
# provide context-aware responses based on user-uploaded files
rag:
  enabled: true
  existingSecret: ''
  configEnv:
    DB_PORT: '5432'
    EMBEDDINGS_PROVIDER: openai

image:
  repository: danny-avila/librechat-rag-api-dev-lite # there is rag-api-dev and rag-api-dev-lite. currently only lite is docuimented
  registry: ghcr.io
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: latest

postgresql:
  enabled: true
  # nameOverride: vectordb
  image:
    registry: ghcr.io
    repository: bat-bs/bitnami-pgvector
    tag: pg16
  auth:
    database: librechat-vectordb
    username: postgres
    # define a secret with values for "postgres-password", "password" (user Password)  and "replication-password" or add values directly
    existingSecret: librechat-vectordb
    # postgres-password is needed to enable pgvector extension. If you enable it manually you can use "password" and user "librechat"
    existingSecretKey: postgres-password

imagePullSecrets: []
nameOverride: ''
fullnameOverride: ''

podAnnotations: {}
podLabels: {}

podSecurityContext: {} # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 8000
  annotations: {}


resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

# livenessProbe:
#   httpGet:
#     path: /
#     port: http
# readinessProbe:
#   httpGet:
#     path: /
#     port: http

# Additional volumes on the output Deployment definition.
volumes: []
# - name: foo
#   secret:
#     secretName: mysecret
#     optional: false

# Additional volumeMounts on the output Deployment definition.
volumeMounts: []
# - name: foo
#   mountPath: "/etc/foo"
#   readOnly: true

nodeSelector: {}

tolerations: []

affinity: {}

extraContainers: {}