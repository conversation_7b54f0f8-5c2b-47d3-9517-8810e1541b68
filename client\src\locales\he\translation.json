{"chat_direction_left_to_right": "חייב להיות כאן תוכן, אין אפשרות להשאיר ריק", "chat_direction_right_to_left": "חייב להיות כאן תוכן, אין אפשרות להשאיר ריק", "com_a11y_ai_composing": "הבינה המלאכותית (AI) עדיין יוצרת", "com_a11y_end": "הבינה המלאכותית (AI) סיימה להשיב.", "com_a11y_start": "הבינה המלאכותית (AI) מתחילה להשיב.", "com_agents_allow_editing": "אפשר למשתמשים אחרים לערוך את הסוכן שלך", "com_agents_by_librechat": "על ידי <PERSON>", "com_agents_code_interpreter": "כאשר מופעל, מא<PERSON><PERSON>ר לסוכן שלך למנף את ה-API של מפענח הקוד  כדי להריץ את הקוד שנוצר, כולל עיבוד קבצים, בצורה מאובטחת. דורש מפתח API חוקי.", "com_agents_code_interpreter_title": "מפע<PERSON><PERSON> קוד API", "com_agents_create_error": "אירעה שגיאה ביצירת הסוכן שלך.", "com_agents_description_placeholder": "אופציונלי: תאר את הסוכן שלך כאן", "com_agents_enable_file_search": "אפ<PERSON>ר ח<PERSON><PERSON><PERSON><PERSON> בקבצים", "com_agents_file_context": "קב<PERSON>י הק<PERSON>ר (OCR)", "com_agents_file_context_disabled": "יש ליצור סוכן לפני שמעלים קבצים עבור הקשר קבצים", "com_agents_file_context_info": "קבצים שהועלו כ\"הקשר\" מעובדים באמצעות OCR (זיהוי אופטי של תווים) כדי להפיק טקסט אשר לאחר מכן מתווסף להוראות הסוכן. אידיאלי עבור מסמכים, תמונות עם טקסט או קובצי PDF בהם אתה צריך את התוכן הטקסטואלי המלא של הקובץ.", "com_agents_file_search_disabled": "יש ליצור את הסוכן לפני העלאת קבצים לחיפוש", "com_agents_file_search_info": "כאשר הסוכן מופעל הוא יקבל מידע על שמות הקבצים המפורטים להלן, כדי שהוא יוכל לאחזר את הקשר רלוונטי.", "com_agents_instructions_placeholder": "הוראות המערכת שבהן ישתמש הסוכן", "com_agents_missing_provider_model": "אנא בחר את הספק ואת הדגם לפני יצירת הסוכן.", "com_agents_name_placeholder": "אופציונלי: שם הסוכן", "com_agents_no_access": "אין לך גישה לערוך את הסוכן הזה.", "com_agents_not_available": "הס<PERSON><PERSON><PERSON> לא זמין", "com_agents_search_name": "<PERSON><PERSON><PERSON> סוכן לפי שם", "com_agents_update_error": "אירעה שגיאה בעדכון הסוכן שלך.", "com_assistants_action_attempt": "הסוכן מעוניין לתקשר עם {{0}}", "com_assistants_actions": "פעולות", "com_assistants_actions_disabled": "עליך ליצור סייען לפני הוספת פעולות.", "com_assistants_actions_info": "אפשר לסייען לאחזר מידע או לבצע פעולות באמצעות API", "com_assistants_add_actions": "הוסף פעולות", "com_assistants_add_tools": "הוסף כלים", "com_assistants_allow_sites_you_trust": "אפשר רק אתרים שאתה סומך עליהם.", "com_assistants_append_date": "הוסף תאריך ושעה נוכחיים", "com_assistants_append_date_tooltip": "כשמופעל, תאריך ושעה נוכחיים של הלקוח יוספים להוראות מערכת הסייען.", "com_assistants_attempt_info": "הסייען רוצה לשלוח את הדברים הבאים:", "com_assistants_available_actions": "פעולות זמינות", "com_assistants_capabilities": "יכולות", "com_assistants_code_interpreter": "מפע<PERSON><PERSON> קוד", "com_assistants_code_interpreter_files": "הקבצים הבאים זמינים רק עבור מפענח קוד:", "com_assistants_code_interpreter_info": "מתורג<PERSON><PERSON> קוד מאפשר לסייען לכתוב ולהריץ קוד. כלי זה יכול לעבד קבצים עם נתונים ועיצוב מגוונים, וליצור קבצים כגון גרפים.", "com_assistants_completed_action": "תקשר עם {{0}}", "com_assistants_completed_function": "מריץ {{0}}", "com_assistants_conversation_starters": "התחלות שיחה", "com_assistants_conversation_starters_placeholder": "ה<PERSON><PERSON><PERSON> פתיח לשיחה", "com_assistants_create_error": "אירעה שגיאה ביצירת הסייען שלך.", "com_assistants_create_success": "נוצר בהצלחה", "com_assistants_delete_actions_error": "אירעה שגיאה במחיקת הפעולה.", "com_assistants_delete_actions_success": "הפעולה נמחקה בהצלחה מהסייען", "com_assistants_description_placeholder": "אופציונלי: תאר את הסייען שלך כאן", "com_assistants_domain_info": "הסייען שלח את המידע ל{{0}}", "com_assistants_file_search": "<PERSON><PERSON><PERSON><PERSON><PERSON> קבצים", "com_assistants_file_search_info": "חיפוש קבצים מאפשר לסייען לקבל ידע מהקבצים שאתה או המשתמשים שלך מעלים. לאחר העלאת קובץ, העוזר מחליט באופן אוטומטי מתי לאחזר תוכן על סמך בקשות המשתמש. אין תמיכה בצירוף מאגרי וקטורים לחיפוש קבצים. אתה יכול לצרף אותם ממגרש החול או לצרף קבצים להודעות לחיפוש קבצים על בסיס שרשור.", "com_assistants_function_use": "הסייען השתמש ב{{0}}", "com_assistants_image_vision": "מציג תמונות", "com_assistants_instructions_placeholder": "הוראות המערכת שהסייען משתמש בהן", "com_assistants_knowledge": "ידע", "com_assistants_knowledge_disabled": "יש ליצור סייען, ויש להפעיל ולשמור את מתורגמן קוד או אחזור לפני העלאת קבצים כ-ידע.", "com_assistants_knowledge_info": "אם אתה מעלה קבצים תחת ידע, שיחות עם ה-סייען שלך עשויות לכלול תוכן מהקובץ.", "com_assistants_max_starters_reached": "הגעת למספר המקסימלי של תווים לפתיח לשיחות", "com_assistants_name_placeholder": "אופציונלי: שם הסייען", "com_assistants_non_retrieval_model": "חי<PERSON><PERSON><PERSON> בקבצים אינו מופעל במודל הזה. אנא בחר מודל אחר", "com_assistants_retrieval": "אח<PERSON><PERSON><PERSON>", "com_assistants_running_action": "פעולות ריצה", "com_assistants_search_name": "<PERSON><PERSON><PERSON> סייען לפי שם", "com_assistants_update_actions_error": "אירעה שגיאה ביצירה או העדכון של הפעולה.", "com_assistants_update_actions_success": "הפעולה נוצרה או עודכנה בהצלחה", "com_assistants_update_error": "אירעה שגיאה בעדכון הסייען שלך.", "com_assistants_update_success": "עוד<PERSON><PERSON> בהצלחה", "com_auth_already_have_account": "כבר יש לך חשבון?", "com_auth_apple_login": "הי<PERSON><PERSON><PERSON> באמצעות חשבון אפל", "com_auth_back_to_login": "חזור להתחברות", "com_auth_click": "קליק", "com_auth_click_here": "לחץ כאן", "com_auth_continue": "המשך", "com_auth_create_account": "צור את החשבון שלך", "com_auth_discord_login": "המשך עם Discord", "com_auth_email": "אימייל (דוא\"ל)", "com_auth_email_address": "כתובת דואר אלקטרוני", "com_auth_email_max_length": "אימייל (דוא\"ל) לא יכול להיות ארוך מ-120 תווים", "com_auth_email_min_length": "אימייל (דו<PERSON>\"ל) חייב להיות בן 6 תווים לפחות", "com_auth_email_pattern": "עליך להזין כתובת אימייל (דוא\"ל) חוקית", "com_auth_email_required": "נדרש דוא\"ל", "com_auth_email_resend_link": "שלח שוב דוא\"ל", "com_auth_email_resent_failed": "נכשלה שליחת דוא\"ל לאימות מחדש", "com_auth_email_resent_success": "דוא\"ל לאימות נשלח שוב בהצלחה", "com_auth_email_verification_failed": "אימות הדוא\"ל נכשל", "com_auth_email_verification_failed_token_missing": "האימו<PERSON> נכשל, <PERSON><PERSON><PERSON> טוקן", "com_auth_email_verification_in_progress": "מאמת את הדוא\"ל שלך, אנא המתן", "com_auth_email_verification_invalid": "אימות הדוא\"ל נכשל", "com_auth_email_verification_redirecting": "מפנה מחדש בעוד {{0}} שניות...", "com_auth_email_verification_resend_prompt": "לא קיבלת את הדוא\"ל?", "com_auth_email_verification_success": "הדוא\"ל אומת בהצלחה", "com_auth_email_verifying_ellipsis": "מאמת...", "com_auth_error_create": "אירעה שגיאה בניסיון לרשום את החשבון שלך. בבקשה נסה שוב.", "com_auth_error_invalid_reset_token": "אסימון איפוס הסיסמה הזה אינו תקף עוד.", "com_auth_error_login": "לא ניתן להתחבר עם המידע שסופק. אנא בדוק את האישורים שלך ונסה שוב.", "com_auth_error_login_ban": "החשבון שלך נחסם באופן זמני עקב הפרות של השירות שלנו.", "com_auth_error_login_rl": "יותר מדי ניסיונות כניסה בזמן קצר. בבקשה נסה שוב מאוחר יותר.", "com_auth_error_login_server": "הייתה שגיאת שרת פנימית. אנא המתן מספר רגעים ונסה שוב.", "com_auth_error_login_unverified": "הדוא\"ל שלך לא אומת. אנא חפש בדוא\"ל שלך קישור לאימות.", "com_auth_facebook_login": "המשך עם פייסבוק", "com_auth_full_name": "שם מלא", "com_auth_github_login": "המשך עם <PERSON>", "com_auth_google_login": "המשך עם Google", "com_auth_here": "<PERSON><PERSON><PERSON>", "com_auth_login": "התח<PERSON>ר", "com_auth_login_with_new_password": "עכשיו אתה יכול להתחבר עם הסיסמה החדשה שלך.", "com_auth_name_max_length": "השם חייב להיות פחות מ-80 תווים", "com_auth_name_min_length": "השם חייב להיות לפחות 3 תווים", "com_auth_name_required": "נדרש שם", "com_auth_no_account": "אין לך חשבון?", "com_auth_password": "סיסמה", "com_auth_password_confirm": "א<PERSON>ר סיסמה", "com_auth_password_forgot": "שכחת את הסיסמה?", "com_auth_password_max_length": "הסיסמה חייבת להיות פחות מ-128 תווים", "com_auth_password_min_length": "הסיסמה חייבת להיות בת 8 תווים לפחות", "com_auth_password_not_match": "הסיסמאות אינן תואמות", "com_auth_password_required": "נדרשת סיסמה", "com_auth_registration_success_generic": "אנא בדוק את הדוא\"ל שלך כדי לאמת את כתובת הדוא\"ל שלך.", "com_auth_registration_success_insecure": "ההרשמה הצליחה", "com_auth_reset_password": "אפס את הסיסמה שלך", "com_auth_reset_password_if_email_exists": "אם קיים חשבון עם דוא\"ל זה, נשלח דוא\"ל עם הוראות לאיפוס סיסמה. אנא הקפד לבדוק גם בתיקיית הספאם שלך.", "com_auth_reset_password_link_sent": "אימייל (דוא\"ל) נשלח", "com_auth_reset_password_success": "איפוס סיסמה הצליח", "com_auth_sign_in": "כניסה", "com_auth_sign_up": "הירשם", "com_auth_submit_registration": "שלח רישום", "com_auth_to_reset_your_password": "כדי לאפס את הסיסמה שלך.", "com_auth_to_try_again": "כדי לנסות שוב.", "com_auth_two_factor": "בדוק את יישום הסיסמה החד-פעמית שלך לקבלת קוד", "com_auth_username": "שם משתמש (או<PERSON><PERSON><PERSON>ונלי)", "com_auth_username_max_length": "שם המשת<PERSON><PERSON> חייב להיות פחות מ-20 תווים", "com_auth_username_min_length": "שם משתמש חייב להיות לפחות 2 תווים", "com_auth_verify_your_identity": "אמת את הזהות שלך", "com_auth_welcome_back": "ברוכים הבאים", "com_click_to_download": "(לחץ כאן להורדה)", "com_download_expired": "(פג תוקף ההורדה)", "com_download_expires": "(לחץ כאן כדי להוריד - יפוג בעוד {{0}}) ", "com_endpoint": "נקודת קצה", "com_endpoint_agent": "<PERSON>ו<PERSON><PERSON>", "com_endpoint_agent_model": "מודל סוכן (מומלץ: GPT-3.5)", "com_endpoint_agent_placeholder": "אנא ב<PERSON>ר <PERSON>ו<PERSON>ן", "com_endpoint_ai": "בינה מלאכותית", "com_endpoint_anthropic_maxoutputtokens": "מספר האסימונים המרבי שניתן להפיק בתגובה. ציין ערך נמוך יותר עבור תגובות קצרות יותר וערך גבוה יותר עבור תגובות ארוכות יותר.", "com_endpoint_anthropic_prompt_cache": "שמירת מטמון מהירה מאפשרת שימוש חוזר בהקשר גדול או בהוראות בקריאות API, תוך הפחתת העלויות וההשהייה", "com_endpoint_anthropic_temp": "נע בין 0 ל-1. ה<PERSON><PERSON><PERSON><PERSON> בטמפ' הקרובה יותר ל-0 עבור בחירה אנליטית / מרובה, וקרוב יותר ל-1 עבור משימות יצירתיות ויצירתיות. אנו ממליצים לשנות את זה או את Top P אבל לא את שניהם.", "com_endpoint_anthropic_thinking": "מאפשר חשי<PERSON>ה פנימית עבור דג<PERSON><PERSON> נתמכים (3.7 Sonnet). הערה: דורש שההגדרה של 'תקציב חשיבה' תהיה נמוכה מ'מקסימום טוקנים לפלט'", "com_endpoint_anthropic_thinking_budget": "קובע את מספר הטוקנים המקסימלי שקלוד רשאי להשתמש בו עבור תהליך החשיבה הפנימי. תקציב גבוה יותר עשוי לשפר את איכות התשובה על ידי מתן אפשרות לניתוח מעמיק יותר של בעיות מורכבות, אם כי קלוד לא בהכרח ישתמש בכל התקציב שהוקצה, במיוחד בטווחים שמעל 32K. הגדרה זו חייבת להיות נמוכה מ'מקסימום טוקנים לפלט'.", "com_endpoint_anthropic_topk": "Top-k משנה את האופן שבו המודל בוחר אסימונים לפלט. Top-k של 1 פירושו שהאסימון שנבחר הוא הסביר ביותר מבין כל האסימונים באוצר המילים של הדגם (נקרא גם פענוח חמדן), בעוד ש-top-k של 3 פירושו שהאסימון הבא נבחר מבין 3 הכי הרבה. אסימונים סבירים (באמצעות טמפרטורה).", "com_endpoint_anthropic_topp": "Top-p משנה את האופן שבו המודל בוחר אסימונים לפלט. אסימונים נבחרים מבין רוב K (ראה פרמטר topK) הסביר לפחות עד שסכום ההסתברויות שלהם שווה לערך העליון-p.", "com_endpoint_assistant": "סייען", "com_endpoint_assistant_model": "מודל סייען", "com_endpoint_assistant_placeholder": "אנא בחר סייען מלוח הצד הימני", "com_endpoint_completion": "השלמה", "com_endpoint_completion_model": "מודל השלמה (מומלץ: GPT-4)", "com_endpoint_config_click_here": "לחץ כאן", "com_endpoint_config_google_api_info": "כדי לקבל את מפתח ה-API של Generative Language (עבור תאומים),", "com_endpoint_config_google_api_key": "מפתח Google API", "com_endpoint_config_google_cloud_platform": "(מ-Google Cloud Platform)", "com_endpoint_config_google_gemini_api": "(Gemini API)", "com_endpoint_config_google_service_key": "מפתח חשבון שירות Google", "com_endpoint_config_key": "הגדר מפתח API", "com_endpoint_config_key_encryption": "המפתח שלך יוצפן וימחק ב", "com_endpoint_config_key_for": "הגדר מפתח API עבור", "com_endpoint_config_key_google_need_to": "אתה צריך", "com_endpoint_config_key_google_service_account": "צור חשבון שירות", "com_endpoint_config_key_google_vertex_ai": "הפעל את Vertex AI", "com_endpoint_config_key_google_vertex_api": "API ב-Google Cloud, אז", "com_endpoint_config_key_google_vertex_api_role": "הקפד ללחוץ על 'צור והמשך' כדי לתת לפחות את התפקיד 'Vertex AI User'. לבסוף, צור מפתח JSON לייבא לכאן.", "com_endpoint_config_key_import_json_key": "ייבוא מפתח JSON של חשבון שירות.", "com_endpoint_config_key_import_json_key_invalid": "מפתח JSON חשבון שירות לא חוקי, האם ייבאת את הקובץ הנכון?", "com_endpoint_config_key_import_json_key_success": "מפתח JSON של חשבון שירות יובא בהצלחה", "com_endpoint_config_key_name": "מפתח", "com_endpoint_config_key_never_expires": "המפתח שלך לא יפוג לעולם", "com_endpoint_config_placeholder": "הגדר את המפתח שלך בתפריט הכותרת לצאט.", "com_endpoint_config_value": "הזן ערך עבור", "com_endpoint_context": "ה<PERSON><PERSON><PERSON>", "com_endpoint_context_info": "המספר המרבי של הטוקנים שניתן להשתמש בהם בחלון ההקשר. השתמש בזה כדי לשלוט בכמה טוקנים ישלחו בכל בקשה. אם לא צוין, יתבצע שימוש בברירת המחדל של המערכת המבוססות על גודל חלון ההקשר ברירת המחדל של המודלים. הגדרת ערכים גבוהים יותר עלולה לגרום לשגיאות ו/או עלות טוקנים גבוהה יותר.", "com_endpoint_context_tokens": "מקסימום טוקנים בחלון ההקשר", "com_endpoint_custom_name": "שם מותאם אישית", "com_endpoint_default": "ברירת מחדל", "com_endpoint_default_blank": "ברירת מחדל: ריק", "com_endpoint_default_empty": "ברירת מחדל: ריקה", "com_endpoint_default_with_num": "ברירת מחדל: {{0}}", "com_endpoint_deprecated": "לא מומלץ - בתהליך הסרה", "com_endpoint_deprecated_info": "נקודת קצה (endpoint) זו מיושנת ועלולה להיות מוסרת בגרסאות עתידיות, אנא השתמש בנקודת הקצה של הסוכן במקום זאת.", "com_endpoint_deprecated_info_a11y": "נקודת הקצה של התוסף מיושנת ועלולה להיות מוסרת בגרסאות עתידיות, אנא השתמש בנקודת הקצה של הסוכן במקום זאת.", "com_endpoint_examples": "הגדרות קבועות מראש", "com_endpoint_export": "ייצוא", "com_endpoint_export_share": "ייצא/שתף", "com_endpoint_frequency_penalty": "עונש תדירות", "com_endpoint_func_hover": "אפ<PERSON>ר שימוש בפלאגינים כפונקציות OpenAI", "com_endpoint_google_custom_name_placeholder": "הגדר שם מותאם אישית עבור Google", "com_endpoint_google_maxoutputtokens": " המספר המרבי של אסימונים שניתן להפיק בתגובה. ציין ערך נמוך יותר עבור תגובות קצרות יותר וערך גבוה יותר עבור תגובות ארוכות יותר.", "com_endpoint_google_temp": "ערכים גבוהים יותר = יותר אקראיים, בעוד שערכים נמוכים יותר = יותר ממוקד ודטרמיניסטי. אנו ממליצים לשנות את זה או את Top P אבל לא את שניהם.", "com_endpoint_google_topk": "Top-k משנה את האופן שבו המודל בוחר אסימונים לפלט. Top-k של 1 פירושו שהאסימון שנבחר הוא הסביר ביותר מבין כל האסימונים באוצר המילים של הדגם (נקרא גם פענוח חמדן), בעוד ש-top-k של 3 פירושו שהאסימון הבא נבחר מבין 3 הכי הרבה. אסימונים סבירים (באמצעות טמפרטורה).", "com_endpoint_google_topp": "Top-p משנה את האופן שבו המודל בוחר אסימונים לפלט. אסימונים נבחרים מרוב K (ראה פרמטר topK) ככל הנראה לפחות עד ה-sum של ההסתברויות שלהם שווה לערך ה-p העליון.", "com_endpoint_instructions_assistants": "עקוף הוראות", "com_endpoint_instructions_assistants_placeholder": "עובר את הוראות הסייען. זה שימושי לשינוי ההתנהגות על בסיס ריצה.", "com_endpoint_max_output_tokens": "אסימוני פלט מרבי", "com_endpoint_message": "הודעה", "com_endpoint_message_new": "הודעה {{0}}", "com_endpoint_message_not_appendable": "ערוך את ההודעה שלך או צור מחדש.", "com_endpoint_my_preset": "ההגדרה המוגדרת מראש שלי", "com_endpoint_no_presets": "אין עדיין הגדרות מוגדרות מראש, השתמש בלחצן ההגדרות כדי ליצור אחת", "com_endpoint_open_menu": "תפריט פתח", "com_endpoint_openai_custom_name_placeholder": "הגדר שם מותאם אישית עבור ChatGPT", "com_endpoint_openai_detail": "ההחלטה לבקשות חזון. \"נמוך\" זול ומהיר יותר, \"גבוה\" מפורט ויקר יותר, ו\"אוטומטי\" יבחר אוטומטית בין השניים על סמך רזולוציית התמונה.", "com_endpoint_openai_freq": "מספר בין -2.0 ל-2.0. ערכים חיוביים מענישים אסימונים חדשים בהתבסס על התדירות הקיימת שלהם בטקסט עד כה, ומקטינים את הסבירות של המודל לחזור על אותה שורה מילה במילה.", "com_endpoint_openai_max": "האסימונים המקסימליים להפיק. האורך הכולל של אסימוני קלט ואסימונים שנוצרו מוגבל על ידי אורך ההקשר של המודל.", "com_endpoint_openai_max_tokens": "שדה 'max_tokens' אופציונלי, הוא מייצג את המספר המרבי של טוקנים שניתן ליצור בהשלמת הצ'אט. האורך הכולל של טוקני קלט והטוקנים שנוצרו מוגבל על ידי אורך ההקשר של המודל. אתה עלול להיתקל בשגיאות אם המספר הזה חורג מטוקני ההקשר המקסימליים.", "com_endpoint_openai_pres": "מספר בין -2.0 ל-2.0. ערכים חיוביים מענישים אסימונים חדשים על סמך האם הם מופיעים בטקסט עד כה, ומגדילים את הסבירות של המודל לדבר על נושאים חדשים.", "com_endpoint_openai_prompt_prefix_placeholder": "הגדר הוראות מותאמות אישית לכלול בהודעת המערכת. ברירת מחדל: אין", "com_endpoint_openai_reasoning_effort": "במודלים o1 בלבד: מגביל את מאמץ ההנמקה במודלים של הגיון. הפחתת מאמץ החשיבה יכולה לגרום לתגובות מהירות יותר ולפחות טוקנים בשימוש בהנמקה בתגובה.", "com_endpoint_openai_resend": "שלח שוב את כל התמונות שצורפו בעבר. הערה: זה יכול להגדיל משמעותית את עלות האסימונים ואתה עלול להיתקל בשגיאות עם קבצים מצורפים רבים של תמונות.", "com_endpoint_openai_resend_files": "שלח שוב את כל הקבצים שצורפו בעבר. הערה: זה יגדיל את עלות הטוקנים, ואתה עלול להיתקל בשגיאות עם קבצים מצורפים רבים.", "com_endpoint_openai_stop": "עד 4 רצפים שבהם ה-API יפסיק לייצר טוקנים נוספים.", "com_endpoint_openai_temp": "ערכים גבוהים יותר = יותר אקראיים, בעוד שערכים נמוכים יותר = יותר ממוקד ודטרמיניסטי. אנו ממליצים לשנות את זה או את Top P אבל לא את שניהם.", "com_endpoint_openai_topp": "חלופה לדגימה עם טמפרטורה, הנקראת דגימת גרעין, שבה המודל מחשיב את תוצאות האסימונים עם מסת ההסתברות top_p. אז 0.1 אומר שרק האסימונים המהווים את מסת ההסתברות העליונה של 10% נחשבים. אנו ממליצים לשנות את זה או את הטמפרטורה אבל לא את שניהם.", "com_endpoint_output": "פלט", "com_endpoint_plug_image_detail": "פרטי תמונה", "com_endpoint_plug_resend_files": "שלח שוב את הקובץ", "com_endpoint_plug_set_custom_instructions_for_gpt_placeholder": "הגדר הוראות מותאמות אישית לכלול בהודעת המערכת. ברירת מחדל: אין", "com_endpoint_plug_skip_completion": "השלמ<PERSON> דילוג", "com_endpoint_plug_use_functions": "השת<PERSON><PERSON> בפונקציות", "com_endpoint_presence_penalty": "עונש נוכחות", "com_endpoint_preset": "הגדרה קבועה מראש", "com_endpoint_preset_custom_name_placeholder": "אין אפשרות להשאיר את השדה הזה ריק, חייב לביות כאן ערך", "com_endpoint_preset_default": "מוגדר כמו הגדרות ברירת המחדל המוגדרת מראש.", "com_endpoint_preset_default_item": "ברירת מחדל:", "com_endpoint_preset_default_none": "אין ברירת מחדל פעילה.", "com_endpoint_preset_default_removed": "איננו עוד ברירת המחדל המוגדרת מראש.", "com_endpoint_preset_delete_confirm": "האם אתה בטוח שברצונך למחוק את הקביעה המוגדרת מראש הזו?", "com_endpoint_preset_delete_error": "אירעה שגיאה במחיקת הקביעה המוגדרת מראש שלך. בבקשה נסה שוב.", "com_endpoint_preset_import": "הגדרה מראש מיובאת!", "com_endpoint_preset_import_error": "אירעה שגיאה בייבוא הקביעה המוגדרת מראש שלך. בבקשה נסה שוב.", "com_endpoint_preset_name": "שם מוגדר מראש", "com_endpoint_preset_save_error": "אירעה שגיאה בשמירת ההגדרה מראש שלך. בבקשה נסה שוב.", "com_endpoint_preset_selected": "הגדרה מראש פעילה!", "com_endpoint_preset_selected_title": "פעיל!", "com_endpoint_preset_title": "הגדרה מראש", "com_endpoint_presets": "presets", "com_endpoint_presets_clear_warning": "האם אתה בטוח שאתה רוצה לנקות את כל הקביעות המוגדרות מראש? זה בלתי הפיך.", "com_endpoint_prompt_cache": "השת<PERSON>ש בשמירה במטמון של הנחיות (פרומפטים)", "com_endpoint_prompt_prefix": "הוראות מותאמות אישית", "com_endpoint_prompt_prefix_assistants": "הוראות נוספות", "com_endpoint_prompt_prefix_assistants_placeholder": "הגדר הוראות נוספות או הקשר על גבי ההנחיות הראשיות של ה-סייען. התעלמו אם ריק.", "com_endpoint_prompt_prefix_placeholder": "הגדר הוראות מותאמות אישית או הקשר. התעלמו אם ריק.", "com_endpoint_reasoning_effort": "מאמץ בתהליך החשיבה", "com_endpoint_save_as_preset": "שמו<PERSON> כתבנית", "com_endpoint_search": "ח<PERSON><PERSON> נקודת קצה לפי שם", "com_endpoint_search_endpoint_models": "חפש מודלים של {{0}}...", "com_endpoint_search_models": "חפש מו", "com_endpoint_search_var": "חפש {{0}}...", "com_endpoint_set_custom_name": "הגדר שם מותאם אישית, למקרה שתוכל למצוא את הקביעה המוגדרת מראש", "com_endpoint_skip_hover": "אפ<PERSON>ר דילוג על שלב ההשלמה, הסוקר את התשובה הסופית ואת השלבים שנוצרו", "com_endpoint_stop": "רצף לעצירה", "com_endpoint_stop_placeholder": "הפרד ערכים על ידי לחיצה על 'Enter'", "com_endpoint_temperature": "טמפרטורה", "com_endpoint_thinking": "חשיבה", "com_endpoint_thinking_budget": "תק<PERSON>יב חשיבה", "com_endpoint_top_k": "Top K", "com_endpoint_top_p": "Top P", "com_endpoint_use_active_assistant": "השתמש ב-סי<PERSON><PERSON>ן פעיל", "com_error_expired_user_key": "המפתח שסופק עבור {{0}} פג ב-{{1}}. אנא ספק מפתח חדש ונסה שוב.", "com_error_files_dupe": "זוהה קובץ כפול", "com_error_files_empty": "אין אפשרות לקבצים ריקים", "com_error_files_process": "אירעה שגיאה במהלך עיבוד הקובץ.", "com_error_files_unsupported_capability": "לא הופעלו התכונות התומכות בסוג קובץ זה.", "com_error_files_upload": "אירעה שגיאה בעת העלאת הקובץ", "com_error_files_upload_canceled": "בקשת העלאת הקובץ בוטלה. הערה: יית<PERSON><PERSON> שהעלאת הקובץ עדיין בעיבוד ותצטרך למחוק אותו בצורה ידנית.", "com_error_files_validation": "אירעה שגיאה במהלך אימות הקובץ.", "com_error_input_length": "מספר הטוקנים של ההודעות האחרונות גבוה מדי, והוא חורג ממגבלת האסימונים ({{0}} בהתאמה). אנא קצר את ההודעה שלך, שנה את גודל ההקשר המקסימלי בפרמטרי השיחה, או התחל שיחה חדשה.", "com_error_invalid_agent_provider": "המודלים של \"{{0}}\" אינם זמינים לשימוש עם סוכנים. אנא עבור להגדרות הסוכן שלך ובחר ספק הזמין כרגע.", "com_error_invalid_user_key": "מפתח שסו<PERSON>ק אינו חוקי. אנא ספק מפתח חוקי ונסה שוב.", "com_error_moderation": "נראה שהתוכן שנשלח סומן על ידי מערכת הניהול שלנו בגלל שהוא אינו תואם את הנחיות הקהילה שלנו. אנחנו לא יכולים להמשיך עם הנושא הספציפי הזה. אם יש לך שאלות או נושאים אחרים שתרצה לחקור, אנא ערוך את ההודעה שלך, או צור שיחה חדשה.", "com_error_no_base_url": "לא נמצאה כתובת URL. אנא ספק כתובת ונסה שוב.", "com_error_no_user_key": "לא נמצא מפתח. אנא ספק מפתח ונסה שוב.", "com_files_filter": "סינון קבצים...", "com_files_no_results": "אין תוצאות", "com_files_number_selected": "{{0}} מתוך {{1}} פריטים נבחרו", "com_files_table": "השדה חייב ל<PERSON><PERSON> תוכן, הוא אינו יכול להישאר ריק", "com_generated_files": "קבצים שנוצרו:", "com_hide_examples": "הסתר דוגמאות", "com_nav_2fa": "אימו<PERSON> דו-שלב<PERSON> (2FA)", "com_nav_account_settings": "הגדרות חשבון", "com_nav_always_make_prod": "ייצר תמיד גרסאות חדשות", "com_nav_archive_created_at": "תאריך ייצור", "com_nav_archive_name": "שם", "com_nav_archived_chats": "שיחות מארכיון", "com_nav_at_command": "@-פקודה", "com_nav_at_command_description": "הפקודה \"@\" משמשת כמנגנון הפעלה/החלפה של נקודות קצה, מודלים, הגדרות קבועות מראש וכו'.", "com_nav_audio_play_error": "שגיאה בהפעלת אודיו: {{0}}", "com_nav_audio_process_error": "שגיאה בעיבוד האודיו: {{0}}", "com_nav_auto_scroll": "Auto-s גלול אל הכי חדש בפתיחה", "com_nav_auto_send_prompts": "שליחת הנחיות (פרומפטים) אוטומטית", "com_nav_auto_send_text": "טקסט לשליחה אוטומטית", "com_nav_auto_send_text_disabled": "הגדר -1 כדי להשבית", "com_nav_auto_transcribe_audio": "תמלול אוטומטי של אודיו", "com_nav_automatic_playback": "הפעלה אוטומטית של ההודעה האחרונה", "com_nav_balance": "לְאַזֵן", "com_nav_browser": "ד<PERSON><PERSON><PERSON><PERSON>", "com_nav_center_chat_input": "מרכז תיבת הצ'אט במסך הברוכים הבאים", "com_nav_change_picture": "שנה תמונה", "com_nav_chat_commands": "פקודות צ'אט", "com_nav_chat_commands_info": "פקודות אלו מופעלות על ידי הקלדת תווים ספציפיים בתחילת ההודעה. כל פקודה מופעלת על ידי הקידומת המיועדת לה. אתה יכול להשבית אותם אם אתה משתמש בתווים אלה לעתים קרובות כדי להתחיל הודעות.", "com_nav_chat_direction": "כיוונון צ'אט", "com_nav_clear_all_chats": "נקה את כל השיחות", "com_nav_clear_cache_confirm_message": "האם אתה בטוח שברצונך לנקות את המטמון?", "com_nav_clear_conversation": "נקה שיחות", "com_nav_clear_conversation_confirm_message": "אתה בטוח שאתה רוצה לנקות את כל השיחות? זה בלתי הפיך.", "com_nav_close_sidebar": "סגור סרגל צד", "com_nav_commands": "פקודות", "com_nav_confirm_clear": "<PERSON><PERSON><PERSON> ניקוי", "com_nav_conversation_mode": "מצב שיחה ", "com_nav_convo_menu_options": "אפשרויות מצב שיחה", "com_nav_db_sensitivity": "רגישות דציבלים", "com_nav_delete_account": "מחיק<PERSON> החשבון", "com_nav_delete_account_button": "מחק את החשבון שלי לצמיתות", "com_nav_delete_account_confirm": "מחק חשבון - אתה בטוח?", "com_nav_delete_account_email_placeholder": "אנא הזן את כתובת הדוא\"ל של החשבון שלך", "com_nav_delete_cache_storage": "מח<PERSON> אחסון מטמון TTS", "com_nav_delete_data_info": "כל הנתונים שלך יימחקו", "com_nav_delete_warning": "אזהרה: פעולה זו תמחק לצמיתות את חשבונך.", "com_nav_edit_chat_badges": "עריכת תוויות צ'אט", "com_nav_enable_cache_tts": "אפשר מטמון ב- TTS", "com_nav_enable_cloud_browser_voice": "השת<PERSON><PERSON> בקולות מבוססי ענן", "com_nav_enabled": "מופעל", "com_nav_engine": "מנוע", "com_nav_enter_to_send": "הק<PERSON> Enter כדי לשלוח את ההודעה", "com_nav_export": "ייצא", "com_nav_export_all_message_branches": "ייצא את כל ענפי ההודעות", "com_nav_export_conversation": "ייצא שיחה", "com_nav_export_filename": "שם קובץ", "com_nav_export_filename_placeholder": "הגדר את שם הקובץ", "com_nav_export_include_endpoint_options": "כלול אפשרויות נקודת קצה", "com_nav_export_recursive": "רקור<PERSON>י<PERSON>י", "com_nav_export_recursive_or_sequential": "רקורסיבי או רציף?", "com_nav_export_type": "סוג", "com_nav_external": "<PERSON><PERSON><PERSON><PERSON><PERSON>י", "com_nav_font_size": "גודל גופן", "com_nav_font_size_base": "מדיום", "com_nav_font_size_lg": "גדול", "com_nav_font_size_sm": "קטן", "com_nav_font_size_xl": "גדול מאוד", "com_nav_font_size_xs": "קט מאוד", "com_nav_help_faq": "עזרה ושאלות נפוצות", "com_nav_hide_panel": "הסתר לוח הצד הימני ביותר", "com_nav_info_code_artifacts": "אפשר הצגה של רכיבי תצוגת קוד ניסיוניים לצד הצ'אט", "com_nav_info_code_artifacts_agent": "אפשר שימוש ברכיבי תצוגת קוד עבור סוכן זה כברירת מחדל, מתווספות הוראות נוספות ספציפיות לשימוש ברכיבי התצוגה אלא אם \"מצב הנחיה מותאם אישית\" מופעל.", "com_nav_info_custom_prompt_mode": "כאשר אפשרות זו מופעלת, הנחיית ברירת המחדל של מערכת רכיבי תצוגה לא תיכלל. כל ההוראות ליצירת רכיבי תצוגה יהיו חייבות להינתן באופן ידני במצב זה.", "com_nav_info_enter_to_send": "כאשר מופעל, לח<PERSON><PERSON><PERSON> על \"ENTER\" תשלח את ההודעה שלך, כאשר מושבת לחיצה על \"Enter\" תוסיף שורה חדשה, ותצטרך ללחוץ על \"CTRL + ENTER\" כדי לשלוח את ההודעה.", "com_nav_info_fork_change_default": "'הודעות ישירות בלבד' כולל רק את הנתיב הישיר להודעה שנבחרה. 'כלול הסתעפויות קשורות' מוסיף את כל ההסתעפויות הקשורות לאורך הנתיב. 'כלול הכל עד כאן/מכאן' כולל את כל ההודעות וההסתעפויות המחוברות.", "com_nav_info_fork_split_target_setting": "כאשר אפשרות זו מופעלת, הפיצול יתחיל מהודעת היעד ועד להודעה האחרונה בשיחה, בהתאם להתנהגות שנבחרה", "com_nav_info_include_shadcnui": "כאשר אפשרות זו מופעלת, ייכללו הוראות לשימוש ברכיבי shadcn/ui. shadcn/ui הוא אוסף של רכיבים לשימוש חוזר שנבנו באמצעות Radix UI ו-Tailwind CSS.\nהערה: ההוראות הללו ארוכות, ולכן כדאי להפעיל אותן רק אם חשוב לך ליידע את מודל ה-LLM (מודל השפה) על הייבוא והרכיבים הנכונים.\nלמידע נוסף על רכיבים אלה, בקר בכתובת: https://ui.shadcn.com/", "com_nav_info_latex_parsing": "כאשר אפשרות זו מופעלת, קוד LaTeX בהודעות יעובד ויוצג כמשוואות מתמטיות. השבתת אפשרות זו עשויה לשפר את הביצועים אם אינך זקוק לעיבוד LaTeX.", "com_nav_info_save_badges_state": "כאשר אפשרות זו מופעלת, מצב תגי הצ'אט יישמר. משמעות הדבר היא שאם תיצור צ'אט חדש, התגים יישארו באותו מצב שהיו בצ'אט הקודם. אם תנטרל אפשרות זו, התגים יחזרו למצב ברירת המחדל בכל פעם שתיצור צ'אט חדש.", "com_nav_info_save_draft": "כאשר אפשרות זו מופעלת, הטקסט והקבצים המצורפים שאתה מזין בטופס הצ'אט יישמרו באופן אוטומטי כטיוטות במכשיר שלך. טיוטות אלו יהיו זמינות גם אם תטען מחדש את הדף או תעבור לשיחה אחרת. הטיוטות נשמרות באופן מקומי במכשיר שלך ונמחקות לאחר שליחת ההודעה.", "com_nav_info_show_thinking": "כאשר אפשרות זו מופעלת, תיבות תצוגה שמציגות את תהליך החשיבה של הבינה המלאכותית יופיעו פתוחות כברירת מחדל, כך שתוכל לראות את תהליך הניתוח בזמן אמת. כאשר האפשרות מושבתת, תיבות הבחירה יישארו סגורות כברירת מחדל, מה שיוצר ממשק נקי וזורם יותר.", "com_nav_info_user_name_display": "כאשר אפשרות זו מופעלת, שם המשתמש של השולח יוצג מעל כל הודעה שאתה שולח. כאשר האפשרות מושבתת, יוצג רק הכיתוב \"אתה\" מעל ההודעות שלך.", "com_nav_lang_arabic": "ערבית (العربية)", "com_nav_lang_auto": "ז<PERSON><PERSON><PERSON><PERSON> באו<PERSON>ן אוטומטי", "com_nav_lang_brazilian_portuguese": "פורטוגזית ברזילאית (Português Brasileiro)", "com_nav_lang_chinese": "סינית (中文)", "com_nav_lang_dutch": "הולנדית (Nederlands)", "com_nav_lang_english": "אנגלית (English)", "com_nav_lang_estonian": "אסטונית (<PERSON><PERSON><PERSON> keel)", "com_nav_lang_finnish": "פינית (Suomi)", "com_nav_lang_french": "צרפתית (Français)", "com_nav_lang_georgian": "ქართული", "com_nav_lang_german": "גרמנית (Deutsch)", "com_nav_lang_hebrew": "עברית", "com_nav_lang_hungarian": "הונגרית", "com_nav_lang_indonesia": "אינדונזית (Indonesia)", "com_nav_lang_italian": "איטל<PERSON>ית (Italiano)", "com_nav_lang_japanese": "יפנית (日本語)", "com_nav_lang_korean": "קוראנית (한국어)", "com_nav_lang_persian": "פרסית", "com_nav_lang_polish": "פולנית (Polski)", "com_nav_lang_portuguese": "פורטוגזית (Português)", "com_nav_lang_russian": "רוסית (Русский)", "com_nav_lang_spanish": "ספרדית (Español)", "com_nav_lang_swedish": "שוודית (Svenska)", "com_nav_lang_thai": "ไทย", "com_nav_lang_traditional_chinese": "סינית מסורתית (繁體中文)", "com_nav_lang_turkish": "טורקית (Türkçe)", "com_nav_lang_vietnamese": "וייטנאמית (Tiếng Việt)", "com_nav_language": "שפה", "com_nav_latex_parsing": "ניתוח LaTeX בהודעות (עשוי להשפיע על הביצועים)", "com_nav_log_out": "צא", "com_nav_long_audio_warning": "העיבוד של טקסטים ארוכים ייקח יותר זמן.", "com_nav_maximize_chat_space": "הגדל את שטח הצ'אט", "com_nav_modular_chat": "אפשר החל<PERSON>ת נקודות קצה באמצע שיחה", "com_nav_my_files": "הקבצים שלי", "com_nav_not_supported": "לא נתמך", "com_nav_open_sidebar": "פתח סרגל צד", "com_nav_playback_rate": "קצב השמעת האודיו", "com_nav_plugin_auth_error": "אירעה שגיאה בניסיון לאמת את הפלאגין הזה. בבקשה נסה שוב.", "com_nav_plugin_install": "הת<PERSON>ן", "com_nav_plugin_search": "תוס<PERSON>י חיפוש", "com_nav_plugin_store": "<PERSON>נ<PERSON><PERSON> פלאגין", "com_nav_plugin_uninstall": "הסר התקנה", "com_nav_plus_command": "פקודות+-", "com_nav_plus_command_description": "הפעל או בטל את הפקודה '+' כדי להוסיף הגדרת תגובות מרובות", "com_nav_profile_picture": "תמונת פרופיל", "com_nav_save_badges_state": "שמור מצב תגים", "com_nav_save_drafts": "שמיר את האפצה באותו מחשב", "com_nav_scroll_button": "לחצן לגלילה עד הסוף", "com_nav_search_placeholder": "<PERSON><PERSON><PERSON> הודעות", "com_nav_send_message": "שלח הודעה", "com_nav_setting_account": "<PERSON><PERSON><PERSON><PERSON>ן", "com_nav_setting_beta": "תכונות ביטא", "com_nav_setting_chat": "צ'אט", "com_nav_setting_data": "בקרות נתונים", "com_nav_setting_general": "כללי", "com_nav_setting_speech": "<PERSON>י<PERSON><PERSON><PERSON>", "com_nav_settings": "הגדרות", "com_nav_shared_links": "קישורים משותפים", "com_nav_show_code": "הצג תמיד את הקוד בעת שימוש במפענח הקוד.", "com_nav_show_thinking": "פתח תצוגות חשיבה כברירת מחדל", "com_nav_slash_command": "פקודת/-", "com_nav_slash_command_description": "הפעל/כבה את הפקודה '/' לבחירת הנחיה (פרומפט) באמצעות המקלדת.", "com_nav_speech_to_text": "דיב<PERSON><PERSON> לטקסט", "com_nav_stop_generating": "עצור את היצירה", "com_nav_text_to_speech": "<PERSON><PERSON><PERSON><PERSON> לדיבור", "com_nav_theme": "נושא", "com_nav_theme_dark": "כהה", "com_nav_theme_light": "אור", "com_nav_theme_system": "מערכת", "com_nav_tool_dialog": "כלי סייען", "com_nav_tool_dialog_agents": "כלי סוכנים", "com_nav_tool_dialog_description": "יש לשמור את האסיסטנט כדי להמשיך בבחירת הכלים.", "com_nav_tool_remove": "הסר", "com_nav_tool_search": "כלי חיפוש", "com_nav_user": "משת<PERSON>ש", "com_nav_user_msg_markdown": "הצגת הודעות משתמש כ-Markdown", "com_nav_user_name_display": "הצג שם משתמש בהודעות", "com_nav_voice_select": "קול", "com_show_agent_settings": "הצג הגדרות סוכן", "com_show_completion_settings": "הצג הגדרות השלמה", "com_show_examples": "הצג דוגמאות", "com_sidepanel_agent_builder": "בניית סוכן", "com_sidepanel_assistant_builder": "בניית סייען", "com_sidepanel_attach_files": "צרף קבצים", "com_sidepanel_conversation_tags": "סימניות", "com_sidepanel_hide_panel": "הסתר פאנל", "com_sidepanel_manage_files": "נהל קבצים", "com_sidepanel_parameters": "פרמטרים", "com_ui_2fa_account_security": "אימות דו-שלבי מוסיף שכבת אבטחה נוספת לחשבון שלך", "com_ui_2fa_disable": "השבת אימות דו-שלבי (2FA)", "com_ui_2fa_disable_error": "התרחשה שגיאה בעת ביטול האימות הדו-שלבי", "com_ui_2fa_disabled": "האימות הדו-שלבי הושבת (2FA)", "com_ui_2fa_enable": "אפשר אימו<PERSON> דו-שלבי (2FA)", "com_ui_2fa_enabled": "האימות הדו-שלבי (2FA) הופעל", "com_ui_2fa_generate_error": "תרחשה שגיאה בעת יצירת הגדרות האימות הדו-שלבי (2FA)", "com_ui_2fa_invalid": "קוד האימות הדו-של<PERSON>י שגוי", "com_ui_2fa_setup": "הגדר אימו<PERSON> דו-שלבי (2FA)", "com_ui_2fa_verified": "האימות הדו-שלבי אומת בהצלחה", "com_ui_accept": "<PERSON><PERSON><PERSON>", "com_ui_add": "הוסף", "com_ui_add_model_preset": "הוספת מודל או הגדרה קבועה לתגובה נוספת", "com_ui_add_multi_conversation": "הוספת תמיכה בשיחות מרובות", "com_ui_admin": "א<PERSON><PERSON><PERSON>ן", "com_ui_admin_access_warning": "השבתת גישת המנהל לתכונה זו עלולה לגרום לבעיות בלתי צפויות בממשק המשתמש שידרשו רענון. אם השינוי נשמר, הדרך היחידה להחזיר את ההגדרה היא דרך הגדרת הממשק בקובץ librechat.yaml, שמשפיעה על כל התפקידים.", "com_ui_admin_settings": "הגדרות אדמין", "com_ui_advanced": "מתקדם", "com_ui_advanced_settings": "הגדרות מתקדמות", "com_ui_agent": "<PERSON>ו<PERSON><PERSON>", "com_ui_agent_chain": "שרשרת סוכנים (תערובת-סוכנים)", "com_ui_agent_chain_info": "מאפשר יצירת שרשרת סוכנים שבה כל סוכן יכול לגשת לפלטים של סוכנים קודמים בשרשרת. מבוסס על ארכיטקטורת \"תערובת-סוכנים\" שבה סוכנים משתמשים בפלטים קודמים כמידע עזר.", "com_ui_agent_chain_max": "הגעת למקסימום של {{0}} סוכנים.", "com_ui_agent_delete_error": "אירעה שגיאה בעת מחיקת הסוכן.", "com_ui_agent_deleted": "הסוכן נמחק בהצלחה.", "com_ui_agent_duplicate_error": "אירעה שגיאה בעת שכפול הסוכן", "com_ui_agent_duplicated": "הסו<PERSON>ן שוכפל בהצלחה", "com_ui_agent_editing_allowed": "משתמשים אחרים יכולים כבר לערוך את הסוכן.", "com_ui_agent_recursion_limit": "מספר מרבי של שלבי סוכן", "com_ui_agent_recursion_limit_info": "מגביל את מספר השלבים שהסוכן יכול לבצע בריצה לפני מתן תגובה סופית. ברירת המחדל היא 25 שלבים. שלב הוא בקשת API של בינה מלאכותית או סבב שימוש בכלי. לדוגמה, אינטראקציה בסיסית עם כלי לוקחת 3 שלבים: בקשה ראשונית, שימוש בכלי, ובקשת המשך.", "com_ui_agent_shared_to_all": "השדה חייב ל<PERSON><PERSON> תוכן, אי אפ<PERSON>ר להשאיר אותו ריק", "com_ui_agent_var": "{{0}} סוכנים", "com_ui_agents": "סוכנים", "com_ui_agents_allow_create": "אפ<PERSON>ר יצירת סוכנים", "com_ui_agents_allow_share_global": "אפשר שיתוף סוכנים לכל המשתמשים", "com_ui_agents_allow_use": "אפ<PERSON>ר שימוש בסוכנים", "com_ui_all": "הכל", "com_ui_all_proper": "הכל", "com_ui_analyzing": "ניתוח", "com_ui_analyzing_finished": "סיים ניתוח", "com_ui_api_key": "מפתח API", "com_ui_archive": "אר<PERSON><PERSON><PERSON>ן", "com_ui_archive_delete_error": "מחיקת השיחה מהארכיון נכשלה", "com_ui_archive_error": "אירעה שגיאה באירכוב השיחה", "com_ui_artifact_click": "לחץ לפתיחה", "com_ui_artifacts": "רכי<PERSON>י תצוגה", "com_ui_artifacts_toggle": "הפעל/כבה רכיבי תצוגה", "com_ui_artifacts_toggle_agent": "אפ<PERSON>ר רכי<PERSON>י תצוגה", "com_ui_ascending": "סדר עולה", "com_ui_assistant": "סייען", "com_ui_assistant_delete_error": "אירעה שגיאה בעת מחיקת הסייען", "com_ui_assistant_deleted": "הסייען נמחק בהצלחה", "com_ui_assistants": "סייען", "com_ui_assistants_output": "פלט סייענים", "com_ui_attach_error": "לא ניתן לצרף קובץ. צור או בחר שיחה, או נסה לרענן את הדף.", "com_ui_attach_error_openai": "לא ניתן לצרף את קבצי הסייען לנקודות קצה אחרות", "com_ui_attach_error_size": "חרגת ממגבלת גודל הקובץ עבור נקודת הקצה:", "com_ui_attach_error_type": "סוג קובץ לא נתמך עבור נקודת קצה:", "com_ui_attach_remove": "הסר קובץ", "com_ui_attach_warn_endpoint": "עשוי להתעלם מקבצים שאינם של הסייען שאין להם כלי תואם", "com_ui_attachment": "קובץ מצורף", "com_ui_auth_type": "סוג אישור", "com_ui_auth_url": "כתובת URL לאימות הרשאה", "com_ui_authentication": "אימות", "com_ui_authentication_type": "סוג אימות", "com_ui_avatar": "אוו<PERSON><PERSON>ר", "com_ui_back_to_chat": "חזור לצ'אט", "com_ui_back_to_prompts": "חזור להנחיות (פרומפטים)", "com_ui_backup_codes": "קו<PERSON>י גיבוי", "com_ui_backup_codes_regenerate_error": "אירעה שגיאה בעת יצירת קודי הגיבוי מחדש", "com_ui_backup_codes_regenerated": "קודי הגיב<PERSON>י נוצרו מחדש בהצלחה", "com_ui_basic": "בסיסי", "com_ui_basic_auth_header": "כותרת אימות בסיסי", "com_ui_bearer": "נושא הרשאה", "com_ui_bookmark_delete_confirm": "האם אתה בטוח שברצונך למחוק את הסימניה הזו?", "com_ui_bookmarks": "סימניות", "com_ui_bookmarks_add": "הוסף סימניות", "com_ui_bookmarks_add_to_conversation": "הוסף לשיחה הנוכחית", "com_ui_bookmarks_count": "ספירה", "com_ui_bookmarks_create_error": "אירעה שגיאה בעת יצירת הסימניה", "com_ui_bookmarks_create_exists": "סימניה זו כבר קיימת", "com_ui_bookmarks_create_success": "הסימניה נוצרה בהצלחה", "com_ui_bookmarks_delete": "<PERSON><PERSON><PERSON>ימ", "com_ui_bookmarks_delete_error": "אירעה שגיאה בעת מחיקת הסימניה", "com_ui_bookmarks_delete_success": "הסימניה נמחקה בהצלחה", "com_ui_bookmarks_description": "תיאור", "com_ui_bookmarks_edit": "ערוך סימניה", "com_ui_bookmarks_filter": "סינון סימניות...", "com_ui_bookmarks_new": "סימניה חדשה", "com_ui_bookmarks_title": "כותרת", "com_ui_bookmarks_update_error": "אירעה שגיאה בעת עדכון הסימניה", "com_ui_bookmarks_update_success": "הסימניה עודכנה בהצלחה", "com_ui_bulk_delete_error": "מחיקת קישורים משותפים נכשלה", "com_ui_callback_url": "כתובת URL להחזרת המידע", "com_ui_cancel": "בטל", "com_ui_category": "קָטֵגוֹרִיָה", "com_ui_chat": "צ'אט", "com_ui_chat_history": "נקה היסטוריה", "com_ui_clear": "נקה", "com_ui_clear_all": "נקה הכל", "com_ui_client_id": "מזהה לקוח", "com_ui_client_secret": "ב", "com_ui_close": "סגור", "com_ui_close_menu": "ס<PERSON><PERSON><PERSON> תפריט", "com_ui_code": "קוד", "com_ui_collapse_chat": "כווץ צ'אט", "com_ui_command_placeholder": "אופציונלי: הזן פקודה להנחיה (פרומ<PERSON><PERSON>), או שיעשה שימוש בשם", "com_ui_command_usage_placeholder": "בחר הנחיה (פרו<PERSON><PERSON><PERSON>) לפי פקודה או שם", "com_ui_complete_setup": "ההגדרה הושלמה", "com_ui_confirm_action": "אשר פעולה", "com_ui_confirm_admin_use_change": "שינוי הגדרה זו יחסום גישה למנהלים, כולל אותך. האם אתה בטוח שברצונך להמשיך?", "com_ui_confirm_change": "אשר את ה<PERSON>וי", "com_ui_context": "ה<PERSON><PERSON><PERSON>", "com_ui_continue": "המשך", "com_ui_controls": "פקדים", "com_ui_convo_delete_error": "מחיקת הצ'אט נכשלה", "com_ui_copied": "הועתק!", "com_ui_copied_to_clipboard": "הועתק ללוח", "com_ui_copy_code": "העתק קוד", "com_ui_copy_link": "העת<PERSON> קישור", "com_ui_copy_to_clipboard": "העתק ללוח", "com_ui_create": "צור", "com_ui_create_link": "<PERSON><PERSON><PERSON> קישור", "com_ui_create_prompt": "צור הנחיה (פרומ<PERSON><PERSON>)", "com_ui_currently_production": "נוצר עכשיו", "com_ui_custom": "מותאם אישית", "com_ui_custom_header_name": "שם כותרת מותאם אישית", "com_ui_custom_prompt_mode": "מצב הנחיה (פרומ<PERSON><PERSON>) מותאם אישית", "com_ui_dashboard": "לוח מחוונים", "com_ui_date": "תאריך", "com_ui_date_april": "אפריל", "com_ui_date_august": "אוגוסט", "com_ui_date_december": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_date_february": "פבר<PERSON><PERSON><PERSON>", "com_ui_date_january": "ינו<PERSON>ר", "com_ui_date_july": "יולי", "com_ui_date_june": "יוני", "com_ui_date_march": "מרץ", "com_ui_date_may": "מאי", "com_ui_date_november": "נובמ<PERSON>ר", "com_ui_date_october": "או<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_date_previous_30_days": "30 ימים אחרונים", "com_ui_date_previous_7_days": "7 ימים אחרונים", "com_ui_date_september": "ספט<PERSON><PERSON>ר", "com_ui_date_today": "היום", "com_ui_date_yesterday": "אתמול", "com_ui_decline": "אני ל<PERSON>ל", "com_ui_default_post_request": "ברירת המחדל (בקשת POST)", "com_ui_delete": "מחק", "com_ui_delete_action": "מח<PERSON> פעולה", "com_ui_delete_action_confirm": "האם אתה בטוח שברצונך למחוק פעולה זו?", "com_ui_delete_agent_confirm": "האם אתה בטוח שברצונך למחוק את הסייען הזה?", "com_ui_delete_assistant_confirm": "האם אתה בטוח שאתה רוצה למחוק את הסייען הזה? אי אפשר לבטל את זה.", "com_ui_delete_confirm": "זה ימחק", "com_ui_delete_confirm_prompt_version_var": "פעולה זו תמחק את הגרסה שנבחרה עבור \"{{0}}\". אם לא קיימות גרסאות נוספות, ההנחיה תימחק.", "com_ui_delete_conversation": "למחוק את השיחה (צאט)?", "com_ui_delete_prompt": "מח<PERSON> הנחיה (פרומ<PERSON><PERSON>)", "com_ui_delete_shared_link": "מח<PERSON> קישור שיתוף", "com_ui_delete_tool": "<PERSON><PERSON><PERSON>לי", "com_ui_delete_tool_confirm": "האת אתה בטוח שאתה רוצה למחוק את הכלי הזה?", "com_ui_descending": "תיאור", "com_ui_description": "תיאור", "com_ui_description_placeholder": "אופציונלי: הזן תיאור שיוצג עבור ההנחיה (פרומפט)", "com_ui_disabling": "מבטל הפעלה...", "com_ui_download": "הורדות", "com_ui_download_artifact": "רכיב תצוגת הורדות", "com_ui_download_backup": "הורד קו<PERSON>י גיבוי", "com_ui_download_backup_tooltip": "לפני שתמשיך, הורד את קודי הגיבוי שלך. תזדקק להם כדי לשחזר גישה במקרה שתאבד את מכשיר האימות שלך", "com_ui_download_error": "וזה: שגיאה בהורדת הקובץ. ייתכן שהקובץ נמחק", "com_ui_drag_drop": "השדה חייב ל<PERSON><PERSON> תוכן, הוא אינו יכול להישאר ריק", "com_ui_dropdown_variables": "רשימה נפתחת של משתנים", "com_ui_dropdown_variables_info": "צור תפריטי רשימה נפתחת מותאמים אישית עבור ההנחיות שלך:\n{{variable_name:option1|option2|option3}}", "com_ui_duplicate": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_duplication_error": "אירעה שגיאה בעת שכפול השיחה", "com_ui_duplication_processing": "משכפל את השיחה...", "com_ui_duplication_success": "השיחה שוכפלה בהצלחה", "com_ui_edit": "ערוך", "com_ui_empty_category": "-", "com_ui_endpoint": "נקודת קצה", "com_ui_endpoint_menu": "תפריט נקודת קצה LLM", "com_ui_enter": "Enter", "com_ui_enter_api_key": "הכנס מפתח API", "com_ui_enter_openapi_schema": "הזן כאן את סכימת OpenAPI שלך", "com_ui_error": "שגיאה", "com_ui_error_connection": "שגיאה בחיבור לשרת, נסה לרענן את הדף", "com_ui_error_save_admin_settings": "אירעה שגיאה בשמירת הגדרות הניהול שלך", "com_ui_examples": "דוגמאות", "com_ui_expand_chat": "הרחב צ'אט", "com_ui_export_convo_modal": "<PERSON><PERSON><PERSON><PERSON> ייצוא שיחה", "com_ui_field_required": "שדה זה נדרש", "com_ui_filter_prompts": "סינון הנחיות (פרומ<PERSON>טים)", "com_ui_filter_prompts_name": "סינון הנחיות (פרומ<PERSON>טים) לפי שם", "com_ui_finance": "פ<PERSON><PERSON><PERSON><PERSON>י", "com_ui_fork": "הסתעפות", "com_ui_fork_all_target": "כלול את כל ההודעות שנשלחו/התקבלו מכאן.", "com_ui_fork_branches": "כלול הסתעפויות קשורות", "com_ui_fork_change_default": "הגדרות הסתעפויות ברירת מחדל", "com_ui_fork_default": "השתמש בהגדרות הסתעפויות ברירת מחדל", "com_ui_fork_error": "אירעה שגיאה בעת פיצול השיחה", "com_ui_fork_from_message": "בחר הגדרת הסתעפויות", "com_ui_fork_info_1": "השתמש בהגדרה זו כדי ליצור הסתעפות של הודעות עם ההתנהגות הרצויה.", "com_ui_fork_info_2": "\"הסתעפות\" מתייחסת ליצירת שיחה חדשה המתחילה/מסתיימת מהודעות ספציפיות בשיחה הנוכחית, תוך יצירת העתק בהתאם לאפשרויות שנבחרו.", "com_ui_fork_info_3": "\"הודעת היעד\" מתייחסת להודעה שממנה נפתחה חלונית זו, או, אם סימנת \"{{0}}\", להודעה האחרונה בשיחה.", "com_ui_fork_info_branches": "אפשרות זו מפצלת את ההודעות הגלויות, יחד עם ההסתעפויות הקשורות; במילים אחרות, המסלול הישיר להודעת היעד, כולל את ההסתעפויות לאורך המסלול.", "com_ui_fork_info_button_label": "הצג מידע על פיצול שיחות", "com_ui_fork_info_remember": "סמן כדי לזכור את האפשרויות שבחרת לשימושים הבאים, כך שתוכל ליצור הסתעפויות בשיחות מהר יותר לפי העדפתך.", "com_ui_fork_info_start": "כאשר מסומן, ההסתעפות תחל מההודעה זו ותימשך עד להודעה האחרונה בשיחה, על פי ההתנהגות שנבחרה לעיל.", "com_ui_fork_info_target": "אפשרות זו תיצור הסתעפות שתכלול את כל ההודעות המובילות להודעת היעד, כולל ההודעות הסמוכות; במילים אחרות, כל ההסתעפויות של ההודעות יכללו, בין אם הם גלויות או לא, ובין אם הם נמצאות באותו מסלול או לא.", "com_ui_fork_info_visible": "אפשרות זו תיצור הסתעפות רק של ההודעות הגלויות; במילים אחרות, רק את המסלול הישיר להודעת היעד, ללא הסתעפויות נוספות.", "com_ui_fork_more_details_about": "הצג מידע ופרטים נוספים על אפשרות פורק \"{{0}}\"", "com_ui_fork_more_info_options": "הצג הסבר מפורט על כל אפשרויות המזלג והתנהגויותיהן", "com_ui_fork_processing": "יוצר הסתעפות בשיחה...", "com_ui_fork_remember": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_fork_remember_checked": "הבחירה שלך תישמר אחרי השימוש. תוכל לשנות זאת בכל זמן בהגדרות.", "com_ui_fork_split_target": "התחל הסתעפות כאן", "com_ui_fork_split_target_setting": "התחל הסתעפות מהודעת היעד כברירת מחדל", "com_ui_fork_success": "יצירת ההסתעפות בשיחה הסתיימה בהצלחה", "com_ui_fork_visible": "הודעות גלויות בלבד", "com_ui_generate_backup": "<PERSON><PERSON><PERSON> קודי גיבוי", "com_ui_generate_qrcode": "<PERSON><PERSON><PERSON> קוד QR", "com_ui_generating": "יוצר...", "com_ui_global_group": "שדה זה לא יכול להישאר ריק", "com_ui_go_back": "חז<PERSON>ר", "com_ui_go_to_conversation": "חזור לצ'אט", "com_ui_good_afternoon": "צהריים טובים", "com_ui_good_evening": "ערב ", "com_ui_good_morning": "ערב טוב", "com_ui_happy_birthday": "זה יום ההולדת הראשון שלי!", "com_ui_hide_qr": "הסתר קוד QR", "com_ui_host": "מאר<PERSON>", "com_ui_idea": "רעיונות", "com_ui_image_gen": "מחולל תמונות", "com_ui_import": "ייבוא", "com_ui_import_conversation_error": "אירעה שגיאה בעת ייבוא השיחות שלך", "com_ui_import_conversation_file_type_error": "סוג ייבוא לא נתמך", "com_ui_import_conversation_info": "ייבא שיחות מקובץ JSON", "com_ui_import_conversation_success": "השיחות יובאו בהצלחה", "com_ui_include_shadcnui": "יש לכלול הוראות לשימוש ברכיבי ממשק המשתמש של shadcn/ui", "com_ui_include_shadcnui_agent": "יש לכלול הוראות שימוש ב-shadcn/ui", "com_ui_input": "קלט", "com_ui_instructions": "הוראות", "com_ui_late_night": "לילה טוב", "com_ui_latest_footer": "גישה לכל הבינות המלאכותיות (AI) לכולם", "com_ui_latest_production_version": "גרסת הפיתוח העדכנית ביותר", "com_ui_latest_version": "גרסה אחרונה", "com_ui_librechat_code_api_key": "קבל את מפתח ה-API של מפענח הקוד LibreChat", "com_ui_librechat_code_api_subtitle": "אבטחה ללא פשרות. תמיכה במגוון שפות תכנות. יכולת עבודה מלאה עם קבצים.", "com_ui_librechat_code_api_title": "הרץ קוד AI", "com_ui_loading": "טוען...", "com_ui_locked": "נעול", "com_ui_logo": "\"לוגו {{0}}\"", "com_ui_manage": "נהל", "com_ui_max_tags": "המספר המקסימלי המותר על פי הערכים העדכניים הוא {{0}}.", "com_ui_mcp_servers": "שרתי MCP", "com_ui_mention": "<PERSON>יין נקודת קצה, סייע<PERSON>, או הנחי<PERSON>ה (פרופמט) כדי לעבור אליה במהירות", "com_ui_min_tags": "לא ניתן למחוק ערכים נוספים, יש צורך במינימום {{0}} ערכים.", "com_ui_misc": "כללי", "com_ui_model": "דגם", "com_ui_model_parameters": "הגדרות המודל", "com_ui_more_info": "מידע נוסף", "com_ui_my_prompts": "ההנחיות (פרומ<PERSON>טים) שלי", "com_ui_name": "שם", "com_ui_new": "ח<PERSON><PERSON>", "com_ui_new_chat": "שיחה חדשה", "com_ui_new_conversation_title": "כותרת חדשה לצ'אט", "com_ui_next": "הבא", "com_ui_no": "לא", "com_ui_no_backup_codes": "אין קודי גיבוי זמינים. אנא צור קודים חדשים", "com_ui_no_bookmarks": "עדיין אין לך סימניות. בחר שיחה והוסף סימניה חדשה", "com_ui_no_category": "<PERSON><PERSON><PERSON>ריה", "com_ui_no_changes": "<PERSON>ין שינויים לעדכן", "com_ui_no_data": "השדה חייב ל<PERSON><PERSON>ל תוכן, הוא לא יכול להישאר ריק", "com_ui_no_terms_content": "אין תוכן תנאים והגבלות להצגה", "com_ui_no_valid_items": "השדה חייב ל<PERSON><PERSON>ל תוכן, הוא לא יכול להישאר ריק", "com_ui_none": "אף אחד", "com_ui_not_used": "לא בשימוש", "com_ui_nothing_found": "לא נמצא", "com_ui_oauth": "פרוטו<PERSON><PERSON><PERSON> אימות פתוח (OAuth)", "com_ui_of": "של", "com_ui_off": "של", "com_ui_on": "פעיל", "com_ui_page": "עמוד", "com_ui_prev": "הקודם", "com_ui_preview": "תצוגה מקדימה", "com_ui_privacy_policy": "מדיניות פרטיות", "com_ui_privacy_policy_url": "קישור למדיניות הפרטיות", "com_ui_prompt": "הנחיה (פרומפט)", "com_ui_prompt_already_shared_to_all": "ההנחיה הזו כבר משותפת עם כל המשתמשים", "com_ui_prompt_name": "שם הנחיה (פרומ<PERSON><PERSON>)", "com_ui_prompt_name_required": "נדרש שם הנחיה (פרומ<PERSON><PERSON>)", "com_ui_prompt_preview_not_shared": "היוצר לא אפשר שיתוף פעולה להנחיה זו", "com_ui_prompt_text": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_prompt_text_required": "נדרש טקסט", "com_ui_prompt_update_error": "אירעה שגיאה בעדכון ההנחיה (פרומ<PERSON><PERSON>)", "com_ui_prompts": "הנחיות (פרומפטים)", "com_ui_prompts_allow_create": "אפ<PERSON>ר יצירת הנחיות", "com_ui_prompts_allow_share_global": "אפשר שיתוף הנחיות (פרומפטים) עם כל המשתמשים", "com_ui_prompts_allow_use": "אפשר שימוש בהנחיות (פרומפטים)", "com_ui_provider": "ספק", "com_ui_read_aloud": "הקראה", "com_ui_redirecting_to_provider": "מבצע הפניה ל-{{0}}, אנא המתן...", "com_ui_refresh_link": "<PERSON><PERSON><PERSON><PERSON><PERSON> קישור", "com_ui_regenerate": "לחדש", "com_ui_regenerate_backup": "<PERSON><PERSON><PERSON> קודי גיבוי מחדש", "com_ui_regenerating": "יוצר מחדש...", "com_ui_region": "איזור", "com_ui_rename": "שנה שם", "com_ui_rename_conversation": "החלפת שם הצ'אט", "com_ui_rename_failed": "החלפת שם הצ'אט נכשלה", "com_ui_rename_prompt": "שנה שם הנחיה (פרומ<PERSON><PERSON>)", "com_ui_requires_auth": "נדרש אימות", "com_ui_reset_var": "איפוס {{0}}", "com_ui_result": "תוצאה", "com_ui_revoke": "בטל", "com_ui_revoke_info": "בטל את כל האישורים שסופקו על ידי המשתמש", "com_ui_revoke_key_confirm": "האם אתה בטוח שברצונך לבטל את המפתח הזה?", "com_ui_revoke_key_endpoint": "ביטול מפתח עבור {{0}}", "com_ui_revoke_keys": "ביטול מפתחות", "com_ui_revoke_keys_confirm": "האם אתה בטוח שברצונך לבטל את כל המפתחות?", "com_ui_role_select": "תפקיד", "com_ui_roleplay": "<PERSON><PERSON><PERSON><PERSON> ת<PERSON>ידים", "com_ui_run_code": "הרץ קו", "com_ui_run_code_error": "אירעה שגיאה בהרצת הקוד", "com_ui_save": "שמור", "com_ui_save_badge_changes": "האם לשמור את השינויים בתגים?", "com_ui_save_submit": "שמור ושלח", "com_ui_saved": "שמור!", "com_ui_schema": "סכמה", "com_ui_scope": "תחום", "com_ui_search": "חיפוש", "com_ui_secret_key": "מפתח סודי", "com_ui_select": "ב<PERSON><PERSON>", "com_ui_select_file": "בחר קובץ", "com_ui_select_model": "<PERSON><PERSON><PERSON> מודל", "com_ui_select_provider": "<PERSON><PERSON><PERSON>", "com_ui_select_provider_first": "ראשית בחר ספק", "com_ui_select_region": "<PERSON><PERSON><PERSON> אי<PERSON><PERSON>ר", "com_ui_select_search_model": "ח<PERSON><PERSON> מודל לפי שם", "com_ui_select_search_plugin": "<PERSON><PERSON><PERSON> פאלגין לפי שם", "com_ui_select_search_provider": "<PERSON><PERSON><PERSON> ספק לפי שם", "com_ui_select_search_region": "ח<PERSON><PERSON> איזור לפי שם", "com_ui_share": "שתף", "com_ui_share_create_message": "שמך וכל הודעה שתוסיף לאחר השיתוף יישארו פרטיים.", "com_ui_share_delete_error": "אירעה שגיאה בעת מחיקת הקישור המשותף.", "com_ui_share_error": "אירעה שגיאה בעת שיתוף קישור הצ'אט", "com_ui_share_form_description": "השדה חייב ל<PERSON><PERSON> תוכן, הוא אינו יכול להישאר ריק", "com_ui_share_link_to_chat": "שתף קישור בצ'אט", "com_ui_share_to_all_users": "שתף עם כל המשתמשים", "com_ui_share_update_message": "השם שלך, ההוראות המותאמות אישית וכל ההודעות שתוסיף לאחר השיתוף יישארו פרטיים.", "com_ui_share_var": "שתף {{0}}", "com_ui_shared_link_bulk_delete_success": "הקישורים המשותפים נמחקו בהצלחה", "com_ui_shared_link_delete_success": "הקישור המשותף נמחק בהצלחה", "com_ui_shared_link_not_found": "הקישור המשותף לא נמצא", "com_ui_shared_prompts": "הנחיות (פרומפטים) משותפות", "com_ui_shop": "קניות", "com_ui_show": "הצג", "com_ui_show_all": "הר<PERSON>ה הכל", "com_ui_show_qr": "הר<PERSON><PERSON> קוד QR", "com_ui_sign_in_to_domain": "הי<PERSON><PERSON><PERSON> אל {{0}}", "com_ui_simple": "פשוט", "com_ui_size": "סוג", "com_ui_special_var_current_date": "תאריך נוכחי", "com_ui_special_var_current_datetime": "תאריך ושעה נוכחיים", "com_ui_special_var_current_user": "משת<PERSON><PERSON> נוכחי", "com_ui_special_var_iso_datetime": "תאריך ושעה ISO UTC", "com_ui_special_variables": "משתנים מיוחדים:", "com_ui_special_variables_more_info": "ניתן לבחור משתנים מיוחדים מהתפריט הנפתח: `{{current_date}}` (תאריך ויום בשבוע של היום), `{{current_datetime}}` (תאריך ושעה מקומיים), `{{utc_iso_datetime}}` (תאריך ושעה UTC ISO) ו-`{{current_user}}` (שם החשבון שלך).", "com_ui_speech_while_submitting": "לא ניתן לשלוח אודיו בזמן שנוצרת תגובה", "com_ui_sr_actions_menu": "פתח את תפריט הפעולות עבור \"{{0}}\"", "com_ui_stop": "עצור", "com_ui_storage": "אח<PERSON><PERSON><PERSON>", "com_ui_submit": "שלח", "com_ui_teach_or_explain": "למידה", "com_ui_temporary": "<PERSON>'<PERSON><PERSON>י", "com_ui_terms_and_conditions": "תנאים והגבלות", "com_ui_terms_of_service": "תנאי השירות", "com_ui_thinking": "חושב...", "com_ui_thoughts": "מחשבות", "com_ui_token_exchange_method": "שיטת החלפת טוקנים", "com_ui_token_url": "קישור URL לטוקן", "com_ui_tools": "כלים", "com_ui_travel": "מסע", "com_ui_unarchive": "ל<PERSON><PERSON><PERSON><PERSON><PERSON>ן", "com_ui_unarchive_error": "אירעה שגיאה בארכיון השיחה", "com_ui_unknown": "לא ידוע", "com_ui_untitled": "ללא כותרת", "com_ui_update": "עדכון", "com_ui_upload": "העלה", "com_ui_upload_code_files": "העלאה עבור מפענח הקוד", "com_ui_upload_delay": "העלאת \"{{0}}\" לוקחת יותר זמן מהצפוי. אנא המתן בזמן שהקובץ מסיים את האינדוקס לאחזור.", "com_ui_upload_error": "אירעה שגיאה בהעלאת הקובץ שלך", "com_ui_upload_file_context": "העלה קובץ כקובץ הקשר", "com_ui_upload_file_search": "העלאה לחיפוש בקבצים", "com_ui_upload_files": "העלה קבצים", "com_ui_upload_image": "העלה תמונה", "com_ui_upload_image_input": "העלה תמונה", "com_ui_upload_invalid": "אין אפשרות להעלות את הקובץ. התמונה חורגת מהמגבלה", "com_ui_upload_invalid_var": "אין אפשרות להעלות את הקובץ. התמונה צריכה להיות בגודל של עד {{0}} MB", "com_ui_upload_ocr_text": "העלה קובץ כקובץ טקסט", "com_ui_upload_success": "הקובץ הועלה בהצלחה", "com_ui_upload_type": "בחר סוג העלאה", "com_ui_use_2fa_code": "השת<PERSON><PERSON> בקוד אימות דו-שלבי (2FA) במקום", "com_ui_use_backup_code": "השת<PERSON><PERSON> בקוד גיבוי במקום", "com_ui_use_micrphone": "שימו<PERSON> במיק<PERSON><PERSON><PERSON><PERSON>ן", "com_ui_use_prompt": "השת<PERSON>ש בהנחיה (פרומ<PERSON><PERSON>)", "com_ui_used": "נוצל", "com_ui_variables": "משתנים", "com_ui_variables_info": "השתמש בסוגריים מסולסלות כפולות בטקסט שלך ליצירת משתנים, לדוגמא  `{{example variable}}`, כדי למלא אותם מאוחר יותר בשימוש בהנחיה.", "com_ui_verify": "אמת", "com_ui_version_var": "גרסה {{0}}", "com_ui_versions": "גרסה", "com_ui_view_source": "הצג צ'אט מקורי", "com_ui_weekend_morning": "סוף שבוע נעים!", "com_ui_write": "כתיבה", "com_ui_x_selected": "{{0}} נב<PERSON>ר", "com_ui_yes": "כן", "com_ui_zoom": "זום", "com_user_message": "אתה", "com_warning_resubmit_unsupported": "שליחת הודעה מחדש אינה נתמכת עבור נקודת קצה זו."}