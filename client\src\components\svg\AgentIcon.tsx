import { cn } from '~/utils';

export default function AgentIcon({
  className = '',
  size = '1em',
}: {
  className?: string;
  size?: string | number;
}) {
  const unit = 24;
  const height = size;
  const width = size;
  return (
    <svg
      width={width}
      height={height}
      viewBox={`0 0 ${unit} ${unit}`}
      stroke="currentColor"
      fill="none"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={cn('text-token-secondary h-2/3 w-2/3', className)}
      xmlns="http://www.w3.org/2000/svg"
    >
      {/* Robot head */}
      <rect x="6" y="6" width="12" height="10" rx="2" ry="2" />
      {/* Robot eyes */}
      <circle cx="9" cy="10" r="1" />
      <circle cx="15" cy="10" r="1" />
      {/* Robot mouth */}
      <path d="M11 13h2" />
      {/* Robot antennas */}
      <path d="M9 6V4" />
      <path d="M15 6V4" />
      <circle cx="9" cy="4" r="1" />
      <circle cx="15" cy="4" r="1" />
      {/* Robot body */}
      <rect x="8" y="16" width="8" height="6" rx="1" ry="1" />
      {/* Robot arms */}
      <path d="M6 18h2" />
      <path d="M16 18h2" />
      {/* Robot legs */}
      <path d="M10 22v2" />
      <path d="M14 22v2" />
    </svg>
  );
}
