{{- if .Values.librechat.imageVolume.enabled }}
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ include "librechat.fullname" $ }}-images
spec:
  accessModes:
    - {{ .Values.librechat.imageVolume.accessModes }}
{{- if .Values.librechat.imageVolume.storageClassName }}
  storageClassName: {{ .Values.librechat.imageVolume.storageClassName }}
{{- end }}          
  volumeMode: Filesystem
  resources:
    requests:
      storage: {{ .Values.librechat.imageVolume.size }}
{{- end }}