{"name": "librechat-data-provider", "version": "0.7.88", "description": "data services for librechat apps", "main": "dist/index.js", "module": "dist/index.es.js", "types": "./dist/types/index.d.ts", "exports": {".": {"import": "./dist/index.es.js", "require": "./dist/index.js", "types": "./dist/types/index.d.ts"}, "./react-query": {"import": "./dist/react-query/index.es.js", "require": "./dist/react-query/index.js", "types": "./dist/types/react-query/index.d.ts"}}, "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "build": "npm run clean && rollup -c --silent --bundleConfigAsCjs", "build:watch": "rollup -c -w", "rollup:api": "npx rollup -c server-rollup.config.js --bundleConfigAsCjs", "test": "jest --coverage --watch", "test:ci": "jest --coverage --ci", "verify": "npm run test:ci", "b:clean": "bun run rimraf dist", "b:build": "bun run b:clean && bun run rollup -c --silent --bundleConfigAsCjs"}, "repository": {"type": "git", "url": "git+https://github.com/danny-a<PERSON>/LibreChat.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://github.com/danny-a<PERSON>/LibreChat/issues"}, "homepage": "https://librechat.ai", "dependencies": {"axios": "^1.8.2", "dayjs": "^1.11.13", "js-yaml": "^4.1.0", "zod": "^3.22.4"}, "devDependencies": {"@babel/preset-env": "^7.21.5", "@babel/preset-react": "^7.18.6", "@babel/preset-typescript": "^7.21.0", "@langchain/core": "^0.3.57", "@rollup/plugin-alias": "^5.1.0", "@rollup/plugin-commonjs": "^25.0.2", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^15.1.0", "@rollup/plugin-replace": "^5.0.5", "@rollup/plugin-terser": "^0.4.4", "@types/jest": "^29.5.2", "@types/js-yaml": "^4.0.9", "@types/node": "^20.3.0", "@types/react": "^18.2.18", "@types/winston": "^2.4.4", "jest": "^29.5.0", "jest-junit": "^16.0.0", "openai": "^4.76.3", "openapi-types": "^12.1.3", "rimraf": "^5.0.1", "rollup": "^4.22.4", "rollup-plugin-generate-package-json": "^3.2.0", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-typescript2": "^0.35.0", "typescript": "^5.0.4"}, "peerDependencies": {"@tanstack/react-query": "^4.28.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}}