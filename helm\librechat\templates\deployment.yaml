apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "librechat.fullname" $ }}
  labels:
    {{- include "librechat.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  {{- if .Values.updateStrategy }}
  strategy: {{- toYaml .Values.updateStrategy | nindent 4 }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "librechat.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      annotations:
        {{ if .Values.librechat.configYamlContent }}checksum/config: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}{{ end }}
        checksum/configEnv: {{ include (print $.Template.BasePath "/configmap-env.yaml") . | sha256sum }}
        {{- with .Values.podAnnotations }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      labels:
        {{- include "librechat.labels" . | nindent 8 }}
        {{- with .Values.podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ include "librechat.fullname" $ }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.registry }}/{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.service.port }}
              protocol: TCP
          lifecycle:
            {{- toYaml .Values.lifecycle | nindent 12 }}
          livenessProbe:
            {{- toYaml .Values.livenessProbe | nindent 12 }}
          readinessProbe:
            {{- toYaml .Values.readinessProbe | nindent 12 }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          volumeMounts:
          {{- if or .Values.librechat.configYamlContent .Values.librechat.existingConfigYaml }}
          - name: config-yaml
            mountPath: "/app/librechat.yaml"
            subPath: "librechat.yaml"
            readOnly: true
          {{- end }}
          {{- if .Values.librechat.imageVolume.enabled }}
          - name: image-volume
            mountPath: "/app/client/public/images"
          {{- end }}
          {{- if .Values.volumeMounts }}
          {{- toYaml .Values.volumeMounts | nindent 10 }}
          {{- end }}
          envFrom:
          - configMapRef:
              name: {{ include "librechat.fullname" $ }}-configenv
          {{- if .Values.global.librechat.existingSecretName }}
          - secretRef:
              name: {{ .Values.global.librechat.existingSecretName }}
              optional: true
          {{- end }}
      volumes:
      {{- if or .Values.librechat.configYamlContent .Values.librechat.existingConfigYaml }}
      - name: config-yaml
        configMap:
        {{- if .Values.librechat.existingConfigYaml }}
          name: {{ .Values.librechat.existingConfigYaml }}
        {{- else if .Values.librechat.configYamlContent }}
          name: {{ include "librechat.fullname" $ }}-config
        {{- end }}
      {{- end }}
      {{- if .Values.librechat.imageVolume.enabled }}
      - name: image-volume
        persistentVolumeClaim:
          claimName: {{ include "librechat.fullname" $ }}-images
      {{- end }}
      {{- if .Values.volumes }}
      {{- toYaml .Values.volumes | nindent 6 }}
      {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
