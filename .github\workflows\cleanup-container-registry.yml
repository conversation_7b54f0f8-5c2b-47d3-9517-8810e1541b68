name: Cleanup Container Registry

on:
  schedule:
    - cron: '0 0 * * *'  # Run daily at midnight UTC
  workflow_dispatch:

jobs:
  cleanup:
    runs-on: ubuntu-latest
    permissions:
      packages: write
      contents: read

    steps:
      - name: Delete untagged images
        uses: actions/delete-package-versions@v5
        with:
          package-name: 'kintu'
          owner: 'BRZEmpreendimentos'
          package-type: 'container'
          min-versions-to-keep: 0
          delete-only-untagged-versions: true
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Delete old SHA-tagged images (keep recent ones)
        uses: actions/delete-package-versions@v5
        with:
          package-name: 'kintu'
          owner: 'BRZEmpreendimentos'
          package-type: 'container'
          min-versions-to-keep: 15
          delete-only-untagged-versions: false
          token: ${{ secrets.GITHUB_TOKEN }}
          # Only delete SHA-tagged images, preserve all named tags
          ignore-versions: '^(latest|develop-latest|main|develop|.*-staging|.*-production)$'

      - name: Delete old temporary/feature branch images
        uses: actions/delete-package-versions@v5
        with:
          package-name: 'kintu'
          owner: 'BRZEmpreendimentos'
          package-type: 'container'
          min-versions-to-keep: 5
          delete-only-untagged-versions: false
          token: ${{ secrets.GITHUB_TOKEN }}
          # Delete feature branch images older than 5 versions, but preserve main tags
          ignore-versions: '^(latest|develop-latest|main|develop|sha-[a-f0-9]{7,}|.*-staging|.*-production)$'

      - name: Log cleanup completion
        run: |
          echo "✅ Container registry cleanup completed successfully!"
          echo "Preserved images:"
          echo "  - latest (production)"
          echo "  - develop-latest (staging/develop)"
          echo "  - main, develop (branch tags)"
          echo "  - *-staging, *-production (environment tags)"
          echo "  - Recent SHA-tagged images (last 15)"
          echo "Cleaned up:"
          echo "  - All untagged images"
          echo "  - Old temporary/feature branch images (keeping last 5)"
          echo "  - Old SHA-tagged images (keeping last 15)"
