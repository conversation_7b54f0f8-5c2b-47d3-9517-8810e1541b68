name: Cleanup Container Registry

on:
  schedule:
    - cron: '0 0 * * *'
  workflow_dispatch:

jobs:
  cleanup:
    runs-on: ubuntu-latest
    permissions:
      packages: write
      contents: read

    steps:
      - name: Delete old container images
        uses: actions/delete-package-versions@v5
        with:
          package-name: 'kintu'
          owner: 'BRZEmpreendimentos'
          package-type: 'container'
          min-versions-to-keep: 5
          delete-only-untagged-versions: false
          token: ${{ secrets.GITHUB_TOKEN }}
          ignore-versions: "latest|dev"
