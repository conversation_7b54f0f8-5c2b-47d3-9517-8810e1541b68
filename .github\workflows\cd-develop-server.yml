name: CD Develop to Server

on:
  workflow_run:
    workflows: ["CD Develop to Container Registry"]
    types:
      - completed
    branches: [ develop ]
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: Development
    if: ${{ github.event.workflow_run.conclusion == 'success' || github.event_name == 'workflow_dispatch' }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: develop

      - name: Install sshpass
        run: sudo apt-get update && sudo apt-get install -y sshpass

      - name: Deploy to DigitalOcean
        env:
          HOST: ${{ secrets.DEPLOY_HOST }}
          USER: ${{ secrets.DEPLOY_USER }}
          PASS: ${{ secrets.DEPLOY_PASSWORD }}
        run: |
          echo "Attempting to connect to $USER@$HOST"
          sshpass -p "$PASS" ssh -o StrictHostKeyChecking=no $USER@$HOST '
            cd ~/kintu && \
            git checkout develop && \
            git pull origin develop && \
            docker compose -f develop-compose.yml down && \
            docker rmi $(docker images -q ghcr.io/brzempreendimentos/kintu:develop-latest) 2>/dev/null || true && \
            docker compose -f develop-compose.yml pull api && \
            docker compose -f develop-compose.yml up -d
          '