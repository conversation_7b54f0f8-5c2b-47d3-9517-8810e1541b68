name: CD to Production

on:
  schedule:
    # Runs every Sunday at 23:00 UTC (10 PM) - Brazil will be 20:00 (8 PM)
    - cron: '0 23 * * 0'
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: Production
    if: ${{ github.event_name == 'schedule' || github.event_name == 'workflow_dispatch' }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Log deployment info
        run: |
          echo "🚀 Starting scheduled production deployment"
          echo "Deployment time: $(date)"
          echo "Triggered by: ${{ github.event_name }}"

      - name: Install sshpass
        run: sudo apt-get update && sudo apt-get install -y sshpass

      - name: Deploy to DigitalOcean
        env:
          HOST: ${{ secrets.DEPLOY_HOST }}
          USER: ${{ secrets.DEPLOY_USER }}
          PASS: ${{ secrets.DEPLOY_PASSWORD }}
        run: |
          echo "Attempting to connect to $USER@$HOST"
          sshpass -p "$PASS" ssh -o StrictHostKeyChecking=no $USER@$HOST '
            echo "Starting scheduled production deployment at $(date)" &&
            cd ~/kintu &&
            echo "Pulling latest code from main branch..." &&
            git pull origin main &&
            echo "Stopping existing containers..." &&
            docker compose -f deploy-compose.yml down &&
            echo "Removing old images..." &&
            docker rmi $(docker images -q ghcr.io/brzempreendimentos/kintu:latest) 2>/dev/null || true &&
            echo "Pulling latest production image..." &&
            docker compose -f deploy-compose.yml pull api &&
            echo "Starting production services..." &&
            docker compose -f deploy-compose.yml up -d &&
            echo "Production deployment completed successfully at $(date)" &&
            echo "Checking service status..." &&
            docker compose -f deploy-compose.yml ps
          '
