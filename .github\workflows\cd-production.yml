name: CD to Production

on:
  workflow_run:
    workflows: ["CD to Container Regestry"]
    types:
      - completed
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    if: ${{ github.event.workflow_run.conclusion == 'success' || github.event_name == 'workflow_dispatch' }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Install sshpass
        run: sudo apt-get update && sudo apt-get install -y sshpass

      - name: Deploy to DigitalOcean
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.DEPLOY_HOST }}
          username: ${{ secrets.DEPLOY_USER }}
          password: ${{ secrets.DEPLOY_PASSWORD }}
          script: |
            cd ~/kintu && \
            git pull origin main && \
            docker compose -f deploy-compose.yml down && \
            docker rmi $(docker images -q ghcr.io/brzempreendimentos/kintu:latest) 2>/dev/null || true && \
            docker compose -f deploy-compose.yml pull api && \
            docker compose -f deploy-compose.yml up -d