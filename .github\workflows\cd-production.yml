name: CD to Production

on:
  workflow_run:
    workflows: ["CD to Container Registry"]
    types:
      - completed
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: Production
    if: ${{ github.event.workflow_run.conclusion == 'success' || github.event_name == 'workflow_dispatch' }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Install sshpass
        run: sudo apt-get update && sudo apt-get install -y sshpass

      - name: Deploy to DigitalOcean
        env:
          HOST: ${{ secrets.DEPLOY_HOST }}
          USER: ${{ secrets.DEPLOY_USER }}
          PASS: ${{ secrets.DEPLOY_PASSWORD }}
        run: |
          echo "Attempting to connect to $USER@$HOST"
          sshpass -p "$PASS" ssh -o StrictHostKeyChecking=no $USER@$HOST '
            cd ~/kintu && \
            git pull origin main && \
            docker compose -f deploy-compose.yml down && \
            docker rmi $(docker images -q ghcr.io/brzempreendimentos/kintu:latest) 2>/dev/null || true && \
            docker compose -f deploy-compose.yml pull api && \
            docker compose -f deploy-compose.yml up -d
          '
