{"com_a11y_end": "AI đã trả lời xong.", "com_auth_already_have_account": "Đã có tài k<PERSON>n?", "com_auth_click": "<PERSON><PERSON><PERSON><PERSON>", "com_auth_click_here": "<PERSON><PERSON><PERSON><PERSON> vào đ<PERSON>y", "com_auth_continue": "<PERSON><PERSON><PERSON><PERSON>", "com_auth_create_account": "<PERSON><PERSON><PERSON> tà<PERSON> k<PERSON>n của bạn", "com_auth_discord_login": "<PERSON><PERSON><PERSON> nhập bằng Discord", "com_auth_email": "Email", "com_auth_email_address": "Đ<PERSON>a chỉ email", "com_auth_email_max_length": "<PERSON><PERSON> kh<PERSON>ng đ<PERSON><PERSON><PERSON> dài hơn 120 ký tự", "com_auth_email_min_length": "<PERSON><PERSON> p<PERSON>i có <PERSON>t nhất 6 ký tự", "com_auth_email_pattern": "<PERSON>ạn ph<PERSON>i nhập địa chỉ email hợp lệ", "com_auth_email_required": "<PERSON><PERSON> l<PERSON> b<PERSON> bu<PERSON>c", "com_auth_error_create": "<PERSON><PERSON> lỗi khi đăng ký tài khoản của bạn. <PERSON><PERSON> lòng thử lại.", "com_auth_error_invalid_reset_token": "<PERSON>ã đặt lại mật khẩu này không còn hợp lệ.", "com_auth_error_login": "<PERSON>hông thể đăng nhập với thông tin được cung cấp. <PERSON><PERSON> lòng kiểm tra thông tin đăng nhập và thử lại.", "com_auth_error_login_ban": "<PERSON><PERSON><PERSON> khoản của bạn đã bị khóa tạm thời do vi phạm dịch vụ của chúng tôi.", "com_auth_error_login_rl": "<PERSON><PERSON><PERSON> nhiều lần đăng nhập trong một khoảng thời gian ngắn. <PERSON><PERSON> lòng thử lại sau.", "com_auth_error_login_server": "<PERSON>ã xảy ra lỗi máy chủ nội bộ. Vui lòng đợi một vài phút và thử lại.", "com_auth_facebook_login": "<PERSON><PERSON><PERSON> nhập bằng Facebook", "com_auth_full_name": "<PERSON><PERSON> và tên đ<PERSON>y đủ", "com_auth_github_login": "<PERSON><PERSON><PERSON> nhập bằng Github", "com_auth_google_login": "<PERSON><PERSON><PERSON> nhập bằng Google", "com_auth_here": "VÀO ĐÂY", "com_auth_login": "<PERSON><PERSON><PERSON>", "com_auth_login_with_new_password": "<PERSON><PERSON>y giờ bạn có thể đăng nhập bằng mật khẩu mới của mình.", "com_auth_name_max_length": "Tên ph<PERSON>i ít hơn 80 ký tự", "com_auth_name_min_length": "<PERSON><PERSON>n ph<PERSON>i có ít nhất 3 ký tự", "com_auth_name_required": "<PERSON><PERSON><PERSON> là b<PERSON><PERSON> bu<PERSON>c", "com_auth_no_account": "Chưa có tài k<PERSON>n?", "com_auth_password": "<PERSON><PERSON><PERSON>", "com_auth_password_confirm": "<PERSON><PERSON><PERSON>n mật kh<PERSON>u", "com_auth_password_forgot": "<PERSON>uên mật khẩu?", "com_auth_password_max_length": "<PERSON><PERSON><PERSON> kh<PERSON>u ph<PERSON>i ít hơn 128 ký tự", "com_auth_password_min_length": "<PERSON><PERSON>t khẩu phải có ít nhất 8 ký tự", "com_auth_password_not_match": "<PERSON><PERSON><PERSON> kh<PERSON>u không khớp", "com_auth_password_required": "<PERSON><PERSON><PERSON> kh<PERSON>u là bắt buộc", "com_auth_reset_password": "Đặt lại mật khẩu", "com_auth_reset_password_link_sent": "<PERSON><PERSON> đ<PERSON> đ<PERSON><PERSON><PERSON>", "com_auth_reset_password_success": "Đặt lại mật khẩu thành công", "com_auth_sign_in": "<PERSON><PERSON><PERSON>", "com_auth_sign_up": "<PERSON><PERSON><PERSON> ký", "com_auth_submit_registration": "<PERSON><PERSON><PERSON> đ<PERSON> ký", "com_auth_to_reset_your_password": "để đặt lại mật khẩu của bạn.", "com_auth_to_try_again": "<PERSON><PERSON> thử lại.", "com_auth_username": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> dù<PERSON> (t<PERSON><PERSON> ch<PERSON>)", "com_auth_username_max_length": "Tên người dùng phải ít hơn 20 ký tự", "com_auth_username_min_length": "<PERSON>ên người dùng phải có ít nhất 2 ký tự", "com_auth_welcome_back": "<PERSON><PERSON><PERSON> mừng trở lại", "com_endpoint": "<PERSON><PERSON><PERSON><PERSON> kết th<PERSON>c", "com_endpoint_agent": "Đặc trưng", "com_endpoint_agent_model": "<PERSON><PERSON> hình Đặc trưng (Đề xuất: GPT-3.5)", "com_endpoint_anthropic_maxoutputtokens": "S<PERSON> mã thông báo tối đa có thể đư<PERSON><PERSON> tạo ra trong phản hồi. Chỉ định một giá trị thấp hơn cho các phản hồi ngắn hơn và một giá trị cao hơn cho các phản hồi dài hơn.", "com_endpoint_anthropic_temp": "Các giá trị nằm trong khoảng từ 0 đến 1. Sử dụng nhiệt độ gần 0 cho các nhiệm vụ phân tích / lựa chọn nhiều lựa chọn, và gần 1 cho các nhiệm vụ sáng tạo và tạo ra. Chúng tôi khuyến nghị thay đổi giá trị này hoặc Top P nhưng không phải cả hai.", "com_endpoint_anthropic_topk": "Top-k thay đổi cách mô hình chọn mã thông báo để xuất. Top-k là 1 có nghĩa là mã thông báo được chọn là phổ biến nhất trong tất cả các mã thông báo trong bảng từ vựng của mô hình (còn được gọi là giải mã tham lam), trong khi top-k là 3 có nghĩa là mã thông báo tiếp theo được chọn từ giữa 3 mã thông báo phổ biến nhất (sử dụng nhiệt độ).", "com_endpoint_anthropic_topp": "Top-p thay đổi cách mô hình chọn mã thông báo để xuất. Mã thông báo được chọn từ căn cứ có xác suất cao nhất đến thấp nhất cho đến khi tổng xác suất của chúng bằng giá trị top-p.", "com_endpoint_completion": "<PERSON><PERSON><PERSON> th<PERSON>", "com_endpoint_completion_model": "<PERSON><PERSON> h<PERSON>nh <PERSON>ành (Đề xuất: GPT-4)", "com_endpoint_config_key": "Đặt Khóa API", "com_endpoint_config_key_encryption": "<PERSON><PERSON><PERSON><PERSON> của bạn sẽ được mã hóa và xóa vào lúc", "com_endpoint_config_key_for": "Đặt Khóa API cho", "com_endpoint_config_key_google_need_to": "<PERSON><PERSON><PERSON> c<PERSON>n", "com_endpoint_config_key_google_service_account": "Tạo một <PERSON> vụ", "com_endpoint_config_key_google_vertex_ai": "Bật Vertex AI", "com_endpoint_config_key_google_vertex_api": "API trên Google Cloud, sau đó", "com_endpoint_config_key_google_vertex_api_role": "<PERSON><PERSON><PERSON> chắc chắn nhấp vào '<PERSON><PERSON>o và Tiếp tục' để cấp ít nhất vai trò 'Người dùng Vertex AI' thì còn lại, tạo một khóa JSON để nhập vào đây.", "com_endpoint_config_key_import_json_key": "Nhập <PERSON>a JSON Tài k<PERSON>n <PERSON> v<PERSON>.", "com_endpoint_config_key_import_json_key_invalid": "Khóa JSON Tài khoản <PERSON>ịch vụ không hợp lệ, Bạn đã nhập đúng tệp không?", "com_endpoint_config_key_import_json_key_success": "<PERSON><PERSON><PERSON><PERSON> thành công Khóa JSON Tài khoản Dị<PERSON> vụ", "com_endpoint_config_key_name": "Khóa", "com_endpoint_config_value": "<PERSON><PERSON><PERSON><PERSON> giá trị cho", "com_endpoint_context": "<PERSON><PERSON> c<PERSON>", "com_endpoint_custom_name": "<PERSON><PERSON>n tùy chỉnh", "com_endpoint_default": "mặc định", "com_endpoint_default_blank": "mặc định: trống", "com_endpoint_default_empty": "mặc định: trống rỗng", "com_endpoint_default_with_num": "mặc định: {{0}}", "com_endpoint_examples": " Đặt sẵn", "com_endpoint_export": "<PERSON><PERSON><PERSON>", "com_endpoint_frequency_penalty": "<PERSON><PERSON><PERSON> p<PERSON>t tần su<PERSON>t", "com_endpoint_func_hover": "<PERSON> phép sử dụng Plugin nh<PERSON> các chức năng OpenAI", "com_endpoint_google_custom_name_placeholder": "Đặt tên tùy chỉnh cho Google", "com_endpoint_google_maxoutputtokens": "S<PERSON> mã thông báo tối đa có thể đư<PERSON><PERSON> tạo ra trong phản hồi. Chỉ định một giá trị thấp hơn cho các phản hồi ngắn hơn và một giá trị cao hơn cho các phản hồi dài hơn.", "com_endpoint_google_temp": "<PERSON><PERSON><PERSON> trị cao = ngẫu nhi<PERSON>n hơn, trong khi giá trị thấp = tập trung và xác định hơn. Chúng tôi khuyến nghị thay đổi giá trị này hoặc Top P nhưng không phải cả hai.", "com_endpoint_google_topk": "Top-k thay đổi cách mô hình chọn mã thông báo để xuất. Top-k là 1 có nghĩa là mã thông báo được chọn là phổ biến nhất trong tất cả các mã thông báo trong bảng từ vựng của mô hình (còn được gọi là giải mã tham lam), trong khi top-k là 3 có nghĩa là mã thông báo tiếp theo được chọn từ giữa 3 mã thông báo phổ biến nhất (sử dụng nhiệt độ).", "com_endpoint_google_topp": "Top-p thay đổi cách mô hình chọn mã thông báo để xuất. Mã thông báo được chọn từ căn cứ có xác suất cao nhất đến thấp nhất cho đến khi tổng xác suất của chúng bằng giá trị top-p.", "com_endpoint_max_output_tokens": "S<PERSON> mã thông báo tối đa", "com_endpoint_my_preset": "Đặt sẵn của tôi", "com_endpoint_no_presets": "<PERSON><PERSON>a có đặt sẵn", "com_endpoint_open_menu": "Mở Menu", "com_endpoint_openai_custom_name_placeholder": "Đặt tên tùy chỉnh cho ChatGPT", "com_endpoint_openai_freq": "Số từ giữa -2.0 và 2.0. <PERSON><PERSON><PERSON> trị dương trừu tượng hóa các mã thông báo mới dựa trên tần suất hiện có của chúng trong văn bản, làm gi<PERSON>m khả năng mô hình lặp lại cùng một dòng văn hoàn toàn.", "com_endpoint_openai_max": "Số mã thông báo tối đa để tạo. Tổng chiều dài của mã thông báo đầu vào và mã thông báo đã tạo bị giới hạn bởi độ dài ngữ cảnh của mô hình.", "com_endpoint_openai_pres": "Số từ giữa -2.0 và 2.0. <PERSON><PERSON><PERSON> trị dương trừu tượng hóa mã thông báo mới dựa trên việc chúng có xuất hiện trong văn bả<PERSON>, làm tăng khả năng của mô hình để nói về các chủ đề mới.", "com_endpoint_openai_prompt_prefix_placeholder": "Đặt hướng dẫn tùy chỉnh để bao gồm vào Thông điệp hệ thống. Mặc định: không có", "com_endpoint_openai_temp": "<PERSON><PERSON><PERSON> trị cao = ngẫu nhi<PERSON>n hơn, trong khi giá trị thấp = tập trung và xác định hơn. Chúng tôi khuyến nghị thay đổi giá trị này hoặc Top P nhưng không phải cả hai.", "com_endpoint_openai_topp": "Một phương pháp thay thế cho việc lấy mẫu nhiệt độ, đ<PERSON><PERSON><PERSON> gọi là lấy mẫu ranh giới, nơi mô hình xem xét kết quả của các mã thông báo với khối lượng xác suất top_p. <PERSON><PERSON> vậy, giá trị 0.1 có nghĩa là chỉ các mã thông báo bao gồm 10% khối lượng xác suất top_p được xem xét. Chúng tôi khuyến nghị thay đổi giá trị này hoặc nhiệt độ nhưng không phải cả hai.", "com_endpoint_output": "<PERSON><PERSON><PERSON> ra", "com_endpoint_plug_set_custom_instructions_for_gpt_placeholder": "Đặt hướng dẫn tùy chỉnh để bao gồm trong Thông điệp hệ thống. Mặc định: không có", "com_endpoint_plug_skip_completion": "Bỏ qua hoàn thành", "com_endpoint_plug_use_functions": "Sử dụng chức n<PERSON>ng", "com_endpoint_presence_penalty": "<PERSON><PERSON><PERSON> p<PERSON> hi<PERSON>", "com_endpoint_preset": "đặt sẵn", "com_endpoint_preset_name": "Tên đặt sẵn", "com_endpoint_presets": "đặt sẵn", "com_endpoint_presets_clear_warning": "Bạn có chắc chắn muốn xóa tất cả các đặt sẵn? Hành động này không thể hoàn tác.", "com_endpoint_prompt_prefix": "<PERSON><PERSON><PERSON><PERSON> tố", "com_endpoint_prompt_prefix_placeholder": "Đặt hướng dẫn hoặc ngữ cảnh tùy chỉnh. Bỏ qua nếu trống.", "com_endpoint_save_as_preset": "<PERSON><PERSON><PERSON> dư<PERSON> dạng đặt sẵn", "com_endpoint_set_custom_name": "Đặt tên tùy chỉnh, nếu bạn có thể tìm thấy cài đặt này", "com_endpoint_skip_hover": "Cho phép bỏ qua bước hoàn thành, kiểm tra câu trả lời cuối cùng và các bước đã tạo", "com_endpoint_temperature": "Nhiệt độ", "com_endpoint_top_k": "Top K", "com_endpoint_top_p": "Top P", "com_nav_auto_scroll": "Cuộn tự động đến tin nhắn mới nhất khi mở", "com_nav_balance": "Sự cân bằng", "com_nav_clear_all_chats": "<PERSON><PERSON><PERSON> tất cả cuộc trò chuyện", "com_nav_clear_conversation": "<PERSON><PERSON><PERSON>c trò chuy<PERSON>n", "com_nav_clear_conversation_confirm_message": "Bạn có chắc chắn muốn xóa tất cả cuộc trò chuyện? Hành động này không thể hoàn tác.", "com_nav_close_sidebar": "<PERSON><PERSON><PERSON> bên", "com_nav_confirm_clear": "<PERSON><PERSON><PERSON>n x<PERSON>a", "com_nav_enabled": "<PERSON><PERSON> bật", "com_nav_export": "<PERSON><PERSON><PERSON>", "com_nav_export_all_message_branches": "<PERSON><PERSON><PERSON> tất cả các nh<PERSON>h tin nh<PERSON>n", "com_nav_export_conversation": "<PERSON><PERSON><PERSON> cu<PERSON>c trò chuy<PERSON>n", "com_nav_export_filename": "<PERSON><PERSON><PERSON>", "com_nav_export_filename_placeholder": "Đặt tên cho tệp", "com_nav_export_include_endpoint_options": "<PERSON><PERSON> gồm các tùy chọn điểm kết thúc", "com_nav_export_recursive": "<PERSON><PERSON> quy", "com_nav_export_recursive_or_sequential": "<PERSON>ệ quy hay tuần tự?", "com_nav_export_type": "<PERSON><PERSON><PERSON>", "com_nav_font_size": "Cỡ chữ", "com_nav_help_faq": "Trợ giúp & Câu hỏi thường gặp", "com_nav_lang_arabic": "العربية", "com_nav_lang_auto": "Tự động phát hiện", "com_nav_lang_brazilian_portuguese": "Portuguê<PERSON>", "com_nav_lang_chinese": "中文", "com_nav_lang_dutch": "Nederlands", "com_nav_lang_english": "English", "com_nav_lang_estonian": "<PERSON><PERSON><PERSON> keel", "com_nav_lang_finnish": "<PERSON><PERSON>", "com_nav_lang_french": "Français ", "com_nav_lang_georgian": "ქართული", "com_nav_lang_german": "De<PERSON>ch", "com_nav_lang_hebrew": "עברית", "com_nav_lang_indonesia": "Indonesia", "com_nav_lang_italian": "Italiano", "com_nav_lang_japanese": "日本語", "com_nav_lang_korean": "한국어", "com_nav_lang_polish": "<PERSON><PERSON>", "com_nav_lang_portuguese": "Português", "com_nav_lang_russian": "Русский", "com_nav_lang_spanish": "Español", "com_nav_lang_swedish": "Svenska", "com_nav_lang_thai": "ไทย", "com_nav_lang_traditional_chinese": "繁體中文", "com_nav_lang_turkish": "Türkçe", "com_nav_lang_vietnamese": "Tiếng <PERSON>", "com_nav_language": "<PERSON><PERSON><PERSON>", "com_nav_log_out": "<PERSON><PERSON><PERSON> xu<PERSON>", "com_nav_not_supported": "<PERSON><PERSON><PERSON><PERSON> được hỗ trợ", "com_nav_open_sidebar": "Mở thanh bên", "com_nav_plugin_auth_error": "<PERSON><PERSON> xảy ra lỗi khi xác thực plugin này. <PERSON><PERSON> lòng thử lại.", "com_nav_plugin_search": "<PERSON><PERSON><PERSON> k<PERSON>m plugin", "com_nav_plugin_store": "<PERSON><PERSON><PERSON>", "com_nav_search_placeholder": "<PERSON><PERSON><PERSON> k<PERSON>ếm tin n<PERSON>n", "com_nav_send_message": "<PERSON><PERSON><PERSON> tin nh<PERSON>n", "com_nav_setting_data": "<PERSON><PERSON><PERSON> so<PERSON>t dữ liệu", "com_nav_setting_general": "<PERSON>", "com_nav_settings": "Cài đặt", "com_nav_shared_links": "<PERSON><PERSON><PERSON> kết đ<PERSON><PERSON><PERSON> chia sẻ", "com_nav_theme": "Chủ đề", "com_nav_theme_dark": "<PERSON><PERSON><PERSON>", "com_nav_theme_light": "<PERSON><PERSON><PERSON>", "com_nav_theme_system": "<PERSON><PERSON> th<PERSON>", "com_nav_user": "NGƯỜI DÙNG", "com_ui_accept": "<PERSON><PERSON><PERSON> chấ<PERSON>n", "com_ui_all": "tất cả", "com_ui_archive": "<PERSON><PERSON><PERSON> tr<PERSON>", "com_ui_archive_error": "<PERSON><PERSON><PERSON><PERSON> thể lưu trữ cuộc trò chuyện", "com_ui_bookmark_delete_confirm": "Bạn có chắc chắn muốn xóa dấu trang này không?", "com_ui_bookmarks": "<PERSON><PERSON><PERSON> trang", "com_ui_bookmarks_add_to_conversation": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON> cu<PERSON> hội thoại hiện tại", "com_ui_bookmarks_count": "Số lượng", "com_ui_bookmarks_create_error": "<PERSON><PERSON> lỗi xảy ra khi tạo dấu trang", "com_ui_bookmarks_create_success": "<PERSON><PERSON><PERSON> d<PERSON>u trang thành công", "com_ui_bookmarks_delete_error": "<PERSON><PERSON> lỗi xảy ra khi x��a dấu trang", "com_ui_bookmarks_delete_success": "<PERSON><PERSON><PERSON> dấu trang thành công", "com_ui_bookmarks_description": "<PERSON><PERSON>", "com_ui_bookmarks_new": "<PERSON><PERSON><PERSON> trang mới", "com_ui_bookmarks_title": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "com_ui_bookmarks_update_error": "<PERSON><PERSON> lỗi x<PERSON>y ra khi cập nhật dấu trang", "com_ui_bookmarks_update_success": "<PERSON><PERSON><PERSON> nhật dấu trang thành công", "com_ui_cancel": "<PERSON><PERSON><PERSON>", "com_ui_clear": "Xóa", "com_ui_close": "Đ<PERSON><PERSON>", "com_ui_confirm_action": "<PERSON><PERSON><PERSON>n hành động", "com_ui_continue": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_copied_to_clipboard": "Đã sao chép vào clipboard", "com_ui_copy_link": "<PERSON><PERSON> ch<PERSON><PERSON> liên kết", "com_ui_copy_to_clipboard": "Sao chép vào clipboard", "com_ui_create_link": "<PERSON><PERSON><PERSON> li<PERSON> k<PERSON>t", "com_ui_decline": "<PERSON><PERSON><PERSON> không chấp nh<PERSON>n", "com_ui_delete": "Xóa", "com_ui_delete_confirm": "<PERSON><PERSON><PERSON>u này sẽ xóa", "com_ui_delete_conversation": "<PERSON><PERSON><PERSON> cuộc trò chuyện?", "com_ui_edit": "<PERSON><PERSON><PERSON>", "com_ui_enter": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_examples": "<PERSON><PERSON>", "com_ui_happy_birthday": "<PERSON><PERSON><PERSON> là sinh nhật đầu tiên của tôi!", "com_ui_import_conversation_error": "<PERSON><PERSON> xảy ra lỗi khi nhập khẩu cuộc trò chuyện của bạn", "com_ui_import_conversation_info": "<PERSON><PERSON><PERSON><PERSON> kh<PERSON>u cuộc trò chuyện từ một tệp JSON", "com_ui_import_conversation_success": "<PERSON><PERSON> nhập kh<PERSON>u cuộc trò chuyện thành công", "com_ui_input": "<PERSON><PERSON><PERSON> vào", "com_ui_model": "<PERSON><PERSON>", "com_ui_new_chat": "<PERSON><PERSON><PERSON> ch<PERSON>n mới", "com_ui_next": "<PERSON><PERSON><PERSON><PERSON> theo", "com_ui_no_terms_content": "<PERSON><PERSON><PERSON><PERSON> có nội dung điều khoản và điều kiện để hiển thị", "com_ui_of": "c<PERSON>a", "com_ui_prev": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_regenerate": "<PERSON><PERSON><PERSON> lạ<PERSON>", "com_ui_rename": "<PERSON><PERSON><PERSON> tên", "com_ui_revoke": "Hủy bỏ", "com_ui_revoke_info": "Hủy bỏ tất cả các thông tin xác thực được cung cấp bởi người dùng.", "com_ui_save": "<PERSON><PERSON><PERSON>", "com_ui_select_model": "<PERSON><PERSON><PERSON> một mô hình", "com_ui_share": "<PERSON><PERSON> sẻ", "com_ui_share_create_message": "Tên của bạn và bất kỳ tin nhắn nào bạn thêm sau khi chia sẻ sẽ được giữ kín.", "com_ui_share_delete_error": "<PERSON><PERSON> xảy ra lỗi khi xóa liên kết đư<PERSON><PERSON> chia sẻ.", "com_ui_share_error": "<PERSON><PERSON> lỗi xảy ra khi chia sẻ liên kết trò chuyện", "com_ui_share_link_to_chat": "<PERSON><PERSON> sẻ liên kết đến cuộc trò chuyện", "com_ui_share_update_message": "Tên củ<PERSON> bạn, hướng dẫn tùy chỉnh và bất kỳ tin nhắn nào bạn thêm sau khi chia sẻ sẽ được giữ kín.", "com_ui_shared_link_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy liên kết chia sẻ", "com_ui_submit": "<PERSON><PERSON><PERSON>", "com_ui_terms_and_conditions": "<PERSON><PERSON><PERSON><PERSON> kho<PERSON>n và điều kiện", "com_ui_unarchive": "Bỏ lưu trữ", "com_ui_unarchive_error": "<PERSON><PERSON><PERSON><PERSON> thể bỏ lưu trữ cuộc trò chuyện", "com_ui_upload_success": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> thành công", "com_ui_use_prompt": "Sử dụng gợi ý"}